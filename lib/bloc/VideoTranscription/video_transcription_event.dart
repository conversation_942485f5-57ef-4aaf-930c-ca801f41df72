part of 'video_transcription_bloc.dart';

sealed class VideoTranscriptionEvent extends Equatable {
  const VideoTranscriptionEvent();

  @override
  List<Object> get props => [];
}

class StartVideoTranscription extends VideoTranscriptionEvent {
  final String videoId;
  final TextEditingController controller;

  const StartVideoTranscription(
      {required this.videoId, required this.controller});

  @override
  List<Object> get props => [videoId];
}

class ProcessVideoTranscription extends VideoTranscriptionEvent {
  final List<Data> captionList;

  const ProcessVideoTranscription({
    required this.captionList,
  });

  @override
  List<Object> get props => [captionList];
}

class CheckTranscriptionStatus extends VideoTranscriptionEvent {
  final int sessionId;

  const CheckTranscriptionStatus({
    required this.sessionId,
  });

  @override
  List<Object> get props => [sessionId];
}

class FetchTranscript extends VideoTranscriptionEvent {
  final int sessionId;
  final int startTime;

  const FetchTranscript({
    required this.sessionId,
    this.startTime = 0,
  });

  @override
  List<Object> get props => [sessionId, startTime];
}

class UpdateCurrentAnimationText extends VideoTranscriptionEvent {
  final String animationText;

  const UpdateCurrentAnimationText({
    required this.animationText,
  });

  @override
  List<Object> get props => [animationText];
}

class VideoTranscriptionCompleted extends VideoTranscriptionEvent {
  final List<Data> captionList;
  final int sessionId;
  final String videoId;

  const VideoTranscriptionCompleted({
    required this.captionList,
    required this.sessionId,
    required this.videoId,
  });

  @override
  List<Object> get props => [captionList, sessionId, videoId];
}

class VideoTranscriptionError extends VideoTranscriptionEvent {
  final String message;

  const VideoTranscriptionError({
    required this.message,
  });

  @override
  List<Object> get props => [message];
}

class ResetVideoTranscription extends VideoTranscriptionEvent {
  final TextEditingController? controller;

  const ResetVideoTranscription({this.controller});

  @override
  List<Object> get props => [];
}

class ToggleInputVisibility extends VideoTranscriptionEvent {
  final bool showInput;

  const ToggleInputVisibility({required this.showInput});

  @override
  List<Object> get props => [showInput];
}

class AddVideoListener extends VideoTranscriptionEvent {
  final YoutubePlayerController controller;

  const AddVideoListener({required this.controller});

  @override
  List<Object> get props => [controller];
}

class SendVideoMessage extends VideoTranscriptionEvent {
  final Map<String, dynamic> message;

  const SendVideoMessage({required this.message});

  @override
  List<Object> get props => [message];
}

class YouTubePlayerReady extends VideoTranscriptionEvent {
  final YoutubePlayerController controller;

  const YouTubePlayerReady({required this.controller});

  @override
  List<Object> get props => [controller];
}

class UpdateAnimationSpeed extends VideoTranscriptionEvent {
  final double value;

  const UpdateAnimationSpeed({required this.value});

  @override
  List<Object> get props => [value];
}

class UpdateSliderValue extends VideoTranscriptionEvent {
  final double value;

  const UpdateSliderValue({required this.value});

  @override
  List<Object> get props => [value];
}
