part of 'text_transcript_bloc.dart';

@immutable
sealed class TextTranscriptEvent {}

/// Event to process text transcription
/// Similar to ProcessTranscription in UnityBloc but simplified for TextTranscriptScreen
class ProcessTextTranscription extends TextTranscriptEvent {
  final String inputText;
  final TextEditingController? inputTextController;
  final FocusNode? inputFocusNode;

  ProcessTextTranscription({
    required this.inputText,
    this.inputTextController,
    this.inputFocusNode,
  });
}

/// Event to clear current transcription results
class ClearTranscription extends TextTranscriptEvent {}

/// Event to reset the transcription state to initial
class ResetTranscription extends TextTranscriptEvent {}

class ReplayTranscription extends TextTranscriptEvent {}

/// Event to update current animation text - matches UpdateCurrentAnimationText
class UpdateCurrentAnimationText extends TextTranscriptEvent {
  final String animationText;

  UpdateCurrentAnimationText({required this.animationText});
}

class UpdateAnimationSpeed extends TextTranscriptEvent {
  final double value;

  UpdateAnimationSpeed({required this.value});
}

class UpdateSliderValue extends TextTranscriptEvent {
  final double value;

  UpdateSliderValue({required this.value});
}

/// Event to change text field status - matches ChangeTextFieldStatus
class ChangeTextFieldStatus extends TextTranscriptEvent {
  final bool isTextFieldEnabled;

  ChangeTextFieldStatus({required this.isTextFieldEnabled});
}

/// Event to change data loading status - matches DataLoading
class DataLoading extends TextTranscriptEvent {
  final bool status;

  DataLoading({required this.status});
}

/// Event to disable/enable translate button - matches DisableSendButton
class DisableTranslateButton extends TextTranscriptEvent {
  final bool status;

  DisableTranslateButton({required this.status});
}

/// Event to disable/enable camera button
class DisableCameraButton extends TextTranscriptEvent {
  final bool status;

  DisableCameraButton({required this.status});
}

/// Event to send animation to Unity - matches SendAnimationToUnity
class SendAnimationToUnity extends TextTranscriptEvent {
  final Map<String, dynamic> message;

  SendAnimationToUnity({required this.message});
}
