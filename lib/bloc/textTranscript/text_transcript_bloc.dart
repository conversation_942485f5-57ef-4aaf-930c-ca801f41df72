import 'dart:async';

import 'package:arabic_sign_language/data/models/text_transcription_model/text_transcription_model.dart';
import 'package:arabic_sign_language/data/service/text_conversion_service.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';

part 'text_transcript_event.dart';
part 'text_transcript_state.dart';

class TextTranscriptBloc
    extends Bloc<TextTranscriptEvent, TextTranscriptState> {
  final TextConversionService textConversionService;
  List<String> rootWords = [];
  List<List<String>> speechTexts = [];
  int speechTextIndex = 0;
  double animationSpeed = 1.0;
  List<TextTranscriptionModel> lastTranscriptionList = [];

  TextTranscriptBloc(this.textConversionService)
      : super(TextTranscriptInitial()) {
    on<ProcessTextTranscription>(_onProcessTextTranscription);
    on<ClearTranscription>(_onClearTranscription);
    on<ResetTranscription>(_onResetTranscription);
    on<UpdateCurrentAnimationText>(_onUpdateCurrentAnimationText);
    on<ChangeTextFieldStatus>(_onChangeTextFieldStatus);
    on<DataLoading>(_onDataLoading);
    on<DisableTranslateButton>(_onDisableTranslateButton);
    on<DisableCameraButton>(_onDisableCameraButton);
    on<SendAnimationToUnity>(_onSendAnimationToUnity);
    on<UpdateAnimationSpeed>(_onUpdateAnimationSpeed);
    on<UpdateSliderValue>(_onUpdateSliderValue);
    on<ReplayTranscription>(_onReplyTranscription);
  }

  Future<void> _onProcessTextTranscription(
    ProcessTextTranscription event,
    Emitter<TextTranscriptState> emit,
  ) async {
    try {
      // Get the transcribed texts - exact same pattern as UnityBloc
      if (event.inputFocusNode != null) {
        event.inputFocusNode!.unfocus();
      }
      emit(const DisableTranslateButtonAction(isTranslateButtonDisabled: true));

      final texts =
          await textConversionService.getTranscribedText(event.inputText);
      await FirebaseAnalytics.instance.logEvent(
          name: "getTranscribedText",
          parameters: {
            "status": "Completed",
            "timeStamp": DateTime.now().toString()
          });

      lastTranscriptionList = texts;

      if (texts.isNotEmpty) {
        if (event.inputTextController != null) {
          // event.inputTextController!.clear();
        }
        emit(const DisableTranslateButtonAction(
            isTranslateButtonDisabled: false));
        emit(const UpdateTextFieldStatus(isTextFieldEnabled: false));

        // Process each text item - exact same logic as UnityBloc
        for (var textItem in texts) {
          final rootWord = textItem.root;
          for (var item in rootWord) {
            rootWords.add(item);
            if (item.contains(',')) {
              final splitWords = item.split(',');
              for (int i = 0; i < splitWords.length; i++) {
                debugPrint(
                    "Iteration: $i, Word: ${splitWords[i]}, Full List: $splitWords");
                await Future.delayed(const Duration(milliseconds: 150), () {
                  emit(TextTranscriptUnityMessage(
                    message: {
                      'root': splitWords[i],
                      'word': textItem.word.toString()
                    },
                    transcriptionList: texts,
                    originalText: event.inputText.trim(),
                    rootWords: List.from(rootWords),
                    currentAnimation: state.currentAnimation,
                    animationSpeed: state.animationSpeed,
                  ));
                });
              }
            } else {
              emit(TextTranscriptUnityMessage(
                message: {
                  'root': item.trim(),
                  'word': textItem.word.toString()
                },
                transcriptionList: texts,
                originalText: event.inputText.trim(),
                rootWords: List.from(rootWords),
                currentAnimation: state.currentAnimation,
                animationSpeed: state.animationSpeed,
              ));
            }
          }
        }

        // Emit final success state
        emit(TextTranscriptSuccess(
          transcriptionList: texts,
          originalText: event.inputText.trim(),
          rootWords: rootWords,
          animationSpeed: state.animationSpeed,
        ));
      } else {
        emit(const TextTranscriptError(
          message: "Invalid Text",
        ));
      }
    } catch (e) {
      emit(TextTranscriptError(
        message: "Failed to process transcription: ${e.toString()}",
      ));
    }
  }

  Future<void> _onReplyTranscription(
    ReplayTranscription event,
    Emitter<TextTranscriptState> emit,
  ) async {
    if (lastTranscriptionList.isNotEmpty) {
      // Process each text item - exact same logic as UnityBloc
      for (var textItem in lastTranscriptionList) {
        final rootWord = textItem.root;
        for (var item in rootWord) {
          rootWords.add(item);
          if (item.contains(',')) {
            final splitWords = item.split(',');
            for (int i = 0; i < splitWords.length; i++) {
              debugPrint(
                  "Iteration: $i, Word: ${splitWords[i]}, Full List: $splitWords");
              await Future.delayed(const Duration(milliseconds: 150), () {
                emit(TextTranscriptUnityMessage(
                  message: {
                    'root': splitWords[i],
                    'word': textItem.word.toString()
                  },
                  transcriptionList: lastTranscriptionList,
                  originalText: "",
                  rootWords: List.from(rootWords),
                  currentAnimation: state.currentAnimation,
                  animationSpeed: state.animationSpeed,
                ));
              });
            }
          } else {
            emit(TextTranscriptUnityMessage(
              message: {'root': item.trim(), 'word': textItem.word.toString()},
              transcriptionList: lastTranscriptionList,
              originalText: "",
              rootWords: List.from(rootWords),
              currentAnimation: state.currentAnimation,
              animationSpeed: state.animationSpeed,
            ));
          }
        }
      }
    }
  }

  void _onClearTranscription(
    ClearTranscription event,
    Emitter<TextTranscriptState> emit,
  ) {
    emit(TextTranscriptInitial());
  }

  void _onResetTranscription(
    ResetTranscription event,
    Emitter<TextTranscriptState> emit,
  ) {
    emit(TextTranscriptInitial());
  }

  // Event handlers matching UnityScreenBloc patterns
  void _onUpdateCurrentAnimationText(
    UpdateCurrentAnimationText event,
    Emitter<TextTranscriptState> emit,
  ) {
    emit(UpdateCurrentAnimation(
      currentAnimation: event.animationText,
      isTextFieldEnabled: state.isTextFieldEnabled,
      isTranslateButtonDisabled: state.isTranslateButtonDisabled,
      isCameraButtonDisabled: state.isCameraButtonDisabled,
      isDataLoading: state.isDataLoading,
      animationSpeed: state.animationSpeed,
    ));
  }

  void _onChangeTextFieldStatus(
    ChangeTextFieldStatus event,
    Emitter<TextTranscriptState> emit,
  ) {
    emit(UpdateTextFieldStatus(isTextFieldEnabled: event.isTextFieldEnabled));
  }

  void _onDataLoading(
    DataLoading event,
    Emitter<TextTranscriptState> emit,
  ) {
    emit(ChangeDataLoading(isDataLoading: event.status));
  }

  void _onDisableTranslateButton(
    DisableTranslateButton event,
    Emitter<TextTranscriptState> emit,
  ) {
    emit(DisableTranslateButtonAction(isTranslateButtonDisabled: event.status));
  }

  void _onDisableCameraButton(
    DisableCameraButton event,
    Emitter<TextTranscriptState> emit,
  ) {
    emit(TextTranscriptSuccess(
      transcriptionList: const [],
      originalText: '',
      rootWords: const [],
      isCameraButtonDisabled: event.status,
    ));
  }

  void _onSendAnimationToUnity(
    SendAnimationToUnity event,
    Emitter<TextTranscriptState> emit,
  ) {
    emit(TextTranscriptUnityMessage(
      message: event.message,
      transcriptionList: const [],
      originalText: '',
      rootWords: const [],
      currentAnimation: state.currentAnimation,
    ));
  }

  // Add new event handler for animation speed
  void _onUpdateAnimationSpeed(
    UpdateAnimationSpeed event,
    Emitter<TextTranscriptState> emit,
  ) {
    // Clamp the value between 0.5 and 2.0
    animationSpeed = event.value.clamp(0.5, 2.0);

    emit(AnimationSpeedUpdated(
      animationSpeed: animationSpeed,
      currentAnimation: state.currentAnimation,
      isTextFieldEnabled: state.isTextFieldEnabled,
      isTranslateButtonDisabled: state.isTranslateButtonDisabled,
      isCameraButtonDisabled: state.isCameraButtonDisabled,
      isDataLoading: state.isDataLoading,
    ));
  }

  // Add new event handler for slider value
  void _onUpdateSliderValue(
    UpdateSliderValue event,
    Emitter<TextTranscriptState> emit,
  ) {
    // Clamp the value between 0 and 1.5 (matching UnityScreen slider range)
    final clampedValue = event.value.clamp(0.0, 1.5);
    // Convert slider value to animation speed (0-1.5 maps to 0.5-2.0)
    animationSpeed = (clampedValue * (2.0 - 0.5) / 1.5) + 0.5;

    emit(SliderValueUpdated(
      sliderValue: clampedValue,
      currentAnimation: state.currentAnimation,
      isTextFieldEnabled: state.isTextFieldEnabled,
      isTranslateButtonDisabled: state.isTranslateButtonDisabled,
      isCameraButtonDisabled: state.isCameraButtonDisabled,
      isDataLoading: state.isDataLoading,
      animationSpeed: animationSpeed,
    ));
  }
}
