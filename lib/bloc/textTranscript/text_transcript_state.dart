part of 'text_transcript_bloc.dart';

@immutable
abstract class TextTranscriptState extends Equatable {
  final String currentAnimation;
  final bool isTextFieldEnabled;
  final bool isTranslateButtonDisabled;
  final bool isCameraButtonDisabled;
  final bool isDataLoading;
  final double animationSpeed;

  const TextTranscriptState({
    this.currentAnimation = '',
    this.isTextFieldEnabled = true,
    this.isTranslateButtonDisabled = false,
    this.isCameraButtonDisabled = false,
    this.isDataLoading = false,
    this.animationSpeed = 1.0,
  });

  @override
  List<Object?> get props => [
        currentAnimation,
        isTextFieldEnabled,
        isTranslateButtonDisabled,
        isCameraButtonDisabled,
        isDataLoading,
        animationSpeed
      ];
}

/// Initial state when the screen is first loaded
class TextTranscriptInitial extends TextTranscriptState {}

/// Loading state when processing transcription
class TextTranscriptLoading extends TextTranscriptState {
  const TextTranscriptLoading({
    super.currentAnimation,
    super.isTextFieldEnabled,
    super.isTranslateButtonDisabled = true,
    super.isCameraButtonDisabled = true,
    super.isDataLoading = true,
  });
}

/// Processing state when sending messages to Unity
class TextTranscriptProcessing extends TextTranscriptState {
  final List<TextTranscriptionModel> transcriptionList;
  final String originalText;

  const TextTranscriptProcessing(
      {required this.transcriptionList,
      required this.originalText,
      super.currentAnimation,
      super.isTextFieldEnabled = false,
      super.isTranslateButtonDisabled = true,
      super.isCameraButtonDisabled = true,
      super.isDataLoading = false,
      super.animationSpeed});

  @override
  List<Object?> get props => [
        transcriptionList,
        originalText,
        currentAnimation,
        isTextFieldEnabled,
        isTranslateButtonDisabled,
        isCameraButtonDisabled,
        isDataLoading,
        animationSpeed
      ];
}

/// State for sending individual messages to Unity - matches SendMessagesToUnity
class TextTranscriptUnityMessage extends TextTranscriptState {
  final Map<String, dynamic> message;
  final List<TextTranscriptionModel> transcriptionList;
  final String originalText;
  final List<String> rootWords;

  const TextTranscriptUnityMessage({
    required this.message,
    required this.transcriptionList,
    required this.originalText,
    required this.rootWords,
    super.currentAnimation,
    super.isTextFieldEnabled = true,
    super.isTranslateButtonDisabled = true,
    super.isCameraButtonDisabled = true,
    super.isDataLoading = false,
    super.animationSpeed,
  });

  @override
  List<Object?> get props => [
        message,
        transcriptionList,
        originalText,
        rootWords,
        currentAnimation,
        isTextFieldEnabled,
        isTranslateButtonDisabled,
        isCameraButtonDisabled,
        isDataLoading,
        animationSpeed
      ];
}

/// Success state when transcription is completed successfully
class TextTranscriptSuccess extends TextTranscriptState {
  final List<TextTranscriptionModel> transcriptionList;
  final String originalText;
  final List<String> rootWords;

  const TextTranscriptSuccess({
    required this.transcriptionList,
    required this.originalText,
    required this.rootWords,
    super.currentAnimation,
    super.isTextFieldEnabled = true,
    super.isTranslateButtonDisabled = false,
    super.isCameraButtonDisabled = false,
    super.isDataLoading = false,
    super.animationSpeed,
  });

  @override
  List<Object?> get props => [
        transcriptionList,
        originalText,
        rootWords,
        currentAnimation,
        isTextFieldEnabled,
        isTranslateButtonDisabled,
        isCameraButtonDisabled,
        isDataLoading,
        animationSpeed,
      ];
}

/// Error state when transcription fails - matches UnityScreenError
class TextTranscriptError extends TextTranscriptState {
  final String message;

  const TextTranscriptError(
      {required this.message,
      super.currentAnimation,
      super.isTextFieldEnabled = true,
      super.isTranslateButtonDisabled = false,
      super.isCameraButtonDisabled = false,
      super.isDataLoading = false,
      super.animationSpeed});

  @override
  List<Object?> get props => [
        message,
        currentAnimation,
        isTextFieldEnabled,
        isTranslateButtonDisabled,
        isCameraButtonDisabled,
        isDataLoading,
        animationSpeed
      ];
}

class AnimationSpeedUpdated extends TextTranscriptState {
  const AnimationSpeedUpdated({
    required double animationSpeed,
    super.currentAnimation,
    super.isTextFieldEnabled,
    super.isTranslateButtonDisabled,
    super.isCameraButtonDisabled,
    super.isDataLoading,
  }) : super(animationSpeed: animationSpeed);

  @override
  List<Object?> get props => [
        animationSpeed,
        currentAnimation,
        isTextFieldEnabled,
        isTranslateButtonDisabled,
        isCameraButtonDisabled,
        isDataLoading,
      ];
}

class SliderValueUpdated extends TextTranscriptState {
  final double sliderValue;

  const SliderValueUpdated({
    required this.sliderValue,
    super.currentAnimation,
    super.isTextFieldEnabled,
    super.isTranslateButtonDisabled,
    super.isCameraButtonDisabled,
    super.isDataLoading,
    super.animationSpeed,
  });

  @override
  List<Object?> get props => [
        sliderValue,
        currentAnimation,
        isTextFieldEnabled,
        isTranslateButtonDisabled,
        isCameraButtonDisabled,
        isDataLoading,
        animationSpeed,
      ];
}

/// State for disabling/enabling translate button - matches DisableSendButtonAction
class DisableTranslateButtonAction extends TextTranscriptState {
  const DisableTranslateButtonAction({
    super.currentAnimation,
    super.isTextFieldEnabled,
    super.isTranslateButtonDisabled = true,
    super.isCameraButtonDisabled,
    super.isDataLoading,
    super.animationSpeed,
  });
}

/// State for updating text field status - matches UpdateTextFieldStatus
class UpdateTextFieldStatus extends TextTranscriptState {
  const UpdateTextFieldStatus({
    super.currentAnimation,
    super.isTextFieldEnabled = false,
    super.isTranslateButtonDisabled,
    super.isCameraButtonDisabled,
    super.isDataLoading,
  });
}

/// State for updating current animation text - matches UpdateCurrentAnimation
class UpdateCurrentAnimation extends TextTranscriptState {
  const UpdateCurrentAnimation({
    super.currentAnimation,
    super.isTextFieldEnabled,
    super.isTranslateButtonDisabled,
    super.isCameraButtonDisabled,
    super.isDataLoading,
    super.animationSpeed,
  });

  @override
  List<Object?> get props => [
        currentAnimation,
        isTextFieldEnabled,
        isTranslateButtonDisabled,
        isCameraButtonDisabled,
        isDataLoading,
        animationSpeed
      ];
}

/// State for changing data loading status - matches ChangeDataLoading
class ChangeDataLoading extends TextTranscriptState {
  const ChangeDataLoading({
    super.currentAnimation,
    super.isTextFieldEnabled,
    super.isTranslateButtonDisabled,
    super.isCameraButtonDisabled,
    super.isDataLoading = true,
  });
}
