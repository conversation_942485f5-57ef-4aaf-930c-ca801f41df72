import 'dart:async';
import 'dart:io';

import 'package:arabic_sign_language/data/service/text_conversion_service.dart';
import 'package:arabic_sign_language/presentation/core/dictionary.dart';
import 'package:asl_flutter_input/asl_flutter_input.dart';
import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:equatable/equatable.dart';
import 'package:bloc/bloc.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:speech_to_text/speech_to_text.dart';

import '../../data/models/video_transcription/transcription_item.dart';
import '../../data/service/recoding_service.dart';
import '../../data/service/transcript_service.dart';

part 'unity_screen_event.dart';
part 'unity_screen_state.dart';

class UnityScreenBloc extends Bloc<UnityScreenEvent, UnityScreenState> {
  final RecordingService recordingService;
  final TextConversionService textConversionService;
  final TranscriptService transcriptService;
  final _aslFlutterInputPlugin = AslFlutterInput();
  Timer? _timer;
  Timer? _speechTimer;
  Timer? _androidSpeechTimer;
  String recognizedWord = '';
  String lastResponse = "";
  String currentResponse = "";
  String previousResult = ""; // Store the previous result
  String newTextToProcess = "";
  String lastProcessedText = ""; // Stable part of the recognized text
  String previousFullText = "";
  List<String> _suggestions = [];
  List<String> previousRecognitions = [];
  final Map<String, List<String>> transformedData = Map.from(dictionary);
  bool isUpdateAlertShown = false;
  List<String> rootWords = [];
  bool enableQueueMessage = false;
  List<List<String>> speechTexts = [];
  List<List<String>> speechWords = [];
  bool isAnimating = false;
  int speechTextIndex = 0;
  int noResponseCount = 0;
  String lastUpdatedText = '';

  UnityScreenBloc(
      this.recordingService, this.textConversionService, this.transcriptService)
      : super(UnityScreenInitial()) {
    on<ToggleVisibility>(changeVisibility);
    on<ChangeRecorderStatus>(changeRecordingStatus);
    on<UpdateRecorderDuration>(updateRecorderDuration);
    on<UpdateCurrentAnimationText>(updateAnimationText);
    on<ChangeTextFieldStatus>(updateTextFieldStatus);
    on<DataLoading>(changeDataLoadingStatus);
    on<StartRecording>(onStartRecording);
    on<StopRecording>(onStopRecording);
    on<RecordingError>(onRecordingError);
    on<ProcessTranscription>(onProcessTranscription);
    on<StartTranscription>(onStartTranscription);
    on<TranscriptionCompleted>(onTranscriptionCompleted);
    on<FetchTranscript>(onFetchTranscript);
    on<CheckTranscriptStatus>(onCheckTranscriptStatus);
    // on<UpdateYoutubeScreenAnimation>(onUpdateYoutubeScreenAnimation);
    on<StartSpeechRecognisation>(onStartRecognisation);
    on<StopSpeechRecognisation>(onStopRecognisation);
    on<RestartSpeechRecognisation>(startSpeechToTextProcessing);
    on<StopRecorder>(onStopRecorder);
    on<DictionarySearch>(onUpdateDictionary);
    on<DictionaryItemSelected>(onDictionaryItemSelected);
    on<UpdateSliderValue>(onUpdateSliderValue);
    on<UpdateAlertStatus>(onUpdateAlertStatus);
    on<UpdateQueueMessage>(onUpdateQueueMessage);
    on<SendUpdatedSpeech>(onSendUpdatedSpeech);
  }

  void changeVisibility(
      ToggleVisibility event, Emitter<UnityScreenState> emit) {
    emit(VisibilityChanged(
      isVisible: event.isVisible,
      isDictionaryOverlay: event.isDictionaryOverlay,
    ));
  }

  void changeRecordingStatus(
      ChangeRecorderStatus event, Emitter<UnityScreenState> emit) {
    emit(EnableRecording(
      isRecordingEnabled: event.isRecordingEnabled,
      isSpeechButtonDisabled: true,
      isVideoButtonDisabled: true,
      isDictionaryButtonDisabled: true,
      isTextFormFieldDisabled: true,
    ));
  }

  void onStopRecorder(StopRecorder event, Emitter<UnityScreenState> emit) {
    emit(DisableRecorder(
      isRecordingEnabled: event.isRecordingEnabled,
      isSpeechButtonDisabled: false,
      isVideoButtonDisabled: false,
    ));
  }

  void updateRecorderDuration(
      UpdateRecorderDuration event, Emitter<UnityScreenState> emit) {
    emit(RecorderDurationUpdated(
        isRecordingEnabled: state.isRecordingEnabled,
        recorderDuration: event.duration,
        isSpeechButtonDisabled: state.isSpeechButtonDisabled,
        isVideoButtonDisabled: state.isVideoButtonDisabled,
        isDictionaryButtonDisabled: state.isDictionaryButtonDisabled,
        isTextFormFieldDisabled: state.isTextFormFieldDisabled));
  }

  void updateAnimationText(
      UpdateCurrentAnimationText event, Emitter<UnityScreenState> emit) {
    emit(UpdateCurrentAnimation(
      isTextToSpeechStart: state.isTextToSpeechStart,
      currentAnimation: event.animation,
      isRecorderButtonDisabled: state.isRecorderButtonDisabled,
      isVideoButtonDisabled: state.isVideoButtonDisabled,
      isDictionaryOverlay: state.isDictionaryOverlay,
      isVisible: state.isVisible,
      isDictionaryButtonDisabled: state.isDictionaryButtonDisabled,
      isTextFormFieldDisabled: state.isTextFormFieldDisabled,
    ));
  }

  void updateTextFieldStatus(
      ChangeTextFieldStatus event, Emitter<UnityScreenState> emit) {
    emit(UpdateTextFieldStatus(
      isTextFieldEnabled: event.status,
    ));
  }

  void changeDataLoadingStatus(
      DataLoading event, Emitter<UnityScreenState> emit) {
    emit(ChangeDataLoading(
      isVisible: state.isVisible,
      isDataLoading: event.status,
    ));
  }

  Future<void> onStartRecording(
      StartRecording event, Emitter<UnityScreenState> emit) async {
    final hasPermission = await event.controller.checkPermission();
    if (hasPermission) {
      await event.controller.record(path: event.path);

      event.controller.onCurrentDuration.listen((duration) {
        add(UpdateRecorderDuration(duration: duration));
      });
    } else {
      emit(const UnityScreenError(message: "Permission denied"));
    }
  }

  Future<void> onStopRecording(
      StopRecording event, Emitter<UnityScreenState> emit) async {
    final path = await event.controller.stop();
    if (path?.isNotEmpty == true) {
      final recording = await File(event.path).readAsBytes();
      final recorderList =
          await recordingService.getTranscriptionFromRecording(recording);
      print("recorderList => $recorderList");

      if (recorderList.isEmpty) {
        emit(const UnityScreenError(
          message: "Please try again",
          isSpeechButtonDisabled: false,
          isVideoButtonDisabled: false,
        ));
      } else {
        for (var item in recorderList) {
          final rootWord = item.root ?? "";
          if (rootWord.contains(',')) {
            final splitWords = rootWord.split(',');
            for (var word in splitWords) {
              emit(SendMessagesToUnity(
                message: {'root': word, 'word': item.word},
                currentAnimation: state.currentAnimation,
              ));
            }
          } else {
            emit(SendMessagesToUnity(
              message: {'root': item.root, 'word': item.word},
              currentAnimation: state.currentAnimation,
            ));
          }
        }
      }
    } else {
      emit(const UnityScreenError(
          message: "Failed to stop recording",
          isSpeechButtonDisabled: false,
          isVideoButtonDisabled: false));
    }

    event.controller.refresh();
    add(UpdateRecorderDuration(duration: Duration.zero));
    add(StopRecorder(isRecordingEnabled: false));
  }

  Future<void> onProcessTranscription(
      ProcessTranscription event, Emitter<UnityScreenState> emit) async {
    // Get the transcribed texts
    event.inputFocusNode.unfocus();
    emit(const DisableSendButtonAction(isRecorderButtonDisabled: true));
    final texts =
        await textConversionService.getTranscribedText(event.inputText);

    if (texts.isNotEmpty) {
      event.inputTextController.clear();
      emit(const DisableSendButtonAction(isRecorderButtonDisabled: false));
      emit(const UpdateTextFieldStatus(isTextFieldEnabled: false));

      // Update the state

      for (var textItem in texts) {
        final rootWord = textItem.root;
        for (var item in rootWord) {
          rootWords.add(item);
          if (item.contains(',')) {
            final splitWords = item.split(',');
            for (int i = 0; i < splitWords.length; i++) {
              print(
                  "Iteration: $i, Word: ${splitWords[i]}, Full List: $splitWords");
              await Future.delayed(const Duration(milliseconds: 150), () {
                emit(SendMessagesToUnity(
                  message: {
                    'root': splitWords[i],
                    'word': textItem.word.toString()
                  },
                  currentAnimation: state.currentAnimation,
                ));
              });
            }
          } else {
            // String temp = "";
            // for (var letter in textItem.root!.split('')) {
            //   temp += letter;
            // }
            // temp = temp.split('').reversed.join('');
            emit(SendMessagesToUnity(
              message: {'root': item.trim(), 'word': textItem.word.toString()},
              currentAnimation: state.currentAnimation,
            ));
          }
        }
      }
    } else {
      if (Platform.isIOS) {
        _speechTimer?.cancel();
      } else {
        _androidSpeechTimer?.cancel();
      }
      emit(const UnityScreenError(
        message: "Invalid Text",
      ));
    }
  }

  void onRecordingError(RecordingError event, Emitter<UnityScreenState> emit) {
    emit(UnityScreenError(message: event.message));
  }

  Future<void> onStartTranscription(
      StartTranscription event, Emitter<UnityScreenState> emit) async {
    emit(ChangeDataLoading(isVisible: state.isVisible, isDataLoading: true));
    await FirebaseAnalytics.instance
        .logEvent(name: "youtube_transcription_started", parameters: {
      "videoId": event.videoId,
      "status": "started",
      "timeStamp": DateTime.now().toString()
    });
    captionList = [];
    final audioStatus = await transcriptService.getVideoStatus(event.videoId);

    if (audioStatus.status) {
      if (audioStatus.data.isNotEmpty) {
        captionList.addAll(audioStatus.data);
        emit(const VisibilityChanged(isVisible: false));
        emit(UpdateCaptionList(
            captionList: audioStatus.data,
            currentAnimation: state.currentAnimation));
        emit(VideoTranscriptionCompleted(
          sessionId: audioStatus.sessionId,
          status: audioStatus.status,
          videoID: event.videoId,
          currentAnimation: state.currentAnimation,
          // captionList: state.captionList,
        ));
        await FirebaseAnalytics.instance
            .logEvent(name: "youtube_transcription_started", parameters: {
          "videoId": event.videoId,
          "status": "returned cached data",
          "timeStamp": DateTime.now().toString()
        });
      } else {
        final data = await transcriptService.getTranscript(
          audioStatus.sessionId,
        );
        emit(ChangeDataLoading(
            isVisible: state.isVisible, isDataLoading: false));

        if (data.data.isNotEmpty) {
          emit(const VisibilityChanged(isVisible: false));

          emit(UpdateCaptionList(
              captionList: data.data,
              currentAnimation: state.currentAnimation));
          emit(VideoTranscriptionCompleted(
            sessionId: audioStatus.sessionId,
            status: data.status,
            videoID: event.videoId,
            currentAnimation: state.currentAnimation,
            // captionList: state.captionList,
          ));
          await FirebaseAnalytics.instance
              .logEvent(name: "youtube_transcription_started", parameters: {
            "videoId": event.videoId,
            "status": "Completed",
            "timeStamp": DateTime.now().toString()
          });
        }
      }
    } else {
      await FirebaseAnalytics.instance
          .logEvent(name: "youtube_transcription_started", parameters: {
        "videoId": event.videoId,
        "status": "upload audio started",
        "timeStamp": DateTime.now().toString()
      });
      final value = await transcriptService.uploadAudioFileToTranscript(
          event.videoId, audioStatus.trackingId);
      // old logic to start transcription and wait 30 seconds to make getCaption Api
      // final value = await transcriptService.startTranscription(event.videoId);
      if (value.status) {
        // await Future.delayed(const Duration(seconds: 30));
        await FirebaseAnalytics.instance
            .logEvent(name: "youtube_transcription_started", parameters: {
          "videoId": event.videoId,
          "status": "fetching captions from audio",
          "timeStamp": DateTime.now().toString()
        });

        await Future.delayed(const Duration(seconds: 30));

        final data = await transcriptService.getTranscript(
          value.sessionId,
        );

        emit(ChangeDataLoading(
            isVisible: state.isVisible, isDataLoading: false));

        if (data.data.isNotEmpty) {
          emit(const VisibilityChanged(isVisible: false));

          emit(UpdateCaptionList(
              captionList: data.data,
              currentAnimation: state.currentAnimation));
          emit(VideoTranscriptionCompleted(
            sessionId: value.sessionId,
            status: data.status,
            videoID: event.videoId,
            currentAnimation: state.currentAnimation,
            // captionList: state.captionList,
          ));
          await FirebaseAnalytics.instance
              .logEvent(name: "youtube_transcription_started", parameters: {
            "videoId": event.videoId,
            "status": "Completed",
            "timeStamp": DateTime.now().toString()
          });
        }
      } else {
        emit(
            ChangeDataLoading(isVisible: state.isVisible, isDataLoading: true));
        emit(const UnityScreenError(
          message: "Error Occurred, Please try again",
        ));
        await FirebaseAnalytics.instance
            .logEvent(name: "youtube_transcription_started", parameters: {
          "videoId": event.videoId,
          "status": "Error while uploading data",
          "timeStamp": DateTime.now().toString()
        });
      }
    }
  }

  void onTranscriptionCompleted(
      TranscriptionCompleted event, Emitter<UnityScreenState> emit) {}

  Future<void> onFetchTranscript(
      FetchTranscript event, Emitter<UnityScreenState> emit) async {
    try {
      final captions = await transcriptService.getTranscript(
        event.videoId,
        startTime: event.startTime,
      );

      if (captions.data.isNotEmpty) {
        final updatedCaptionList = List<Data>.from(state.captionList)
          ..addAll(captions.data);
        emit(UpdateCaptionList(
            captionList: updatedCaptionList,
            currentAnimation: state.currentAnimation));
      }
    } catch (e) {
      print('Error fetching transcript: $e');
    }
  }

  void onCheckTranscriptStatus(
      CheckTranscriptStatus event, Emitter<UnityScreenState> emit) {
    _timer = Timer.periodic(const Duration(seconds: 3), (_) async {
      try {
        final lastCaption =
            state.captionList.isNotEmpty ? state.captionList.last : null;

        if (lastCaption != null && lastCaption.status == 'IN PROGRESS') {
          add(FetchTranscript(
            videoId: event.sessionId,
            startTime: lastCaption.endTime.toInt(),
          ));
        } else {
          _timer?.cancel();
        }
      } catch (e) {
        print('Error checking transcript status: $e');
        _timer?.cancel();
      }
    });
  }

  // void onUpdateYoutubeScreenAnimation(
  //     UpdateYoutubeScreenAnimation event, Emitter<UnityScreenState> emit) {
  //   final rootWord = event.captionData.root;
  //   if (rootWord.contains(',')) {
  //     final splitWords = rootWord.split(',');
  //     for (var word in splitWords) {
  //       emit(SendMessagesToUnity(
  //         message: {'root': word, 'word': event.captionData.word},
  //         currentAnimation: state.currentAnimation,
  //       ));
  //     }
  //   } else {
  //     emit(SendMessagesToUnity(
  //       message: {
  //         'root': event.captionData.root,
  //         'word': event.captionData.word
  //       },
  //       currentAnimation: state.currentAnimation,
  //     ));
  //   }
  // }

  Future<void> onStartRecognisation(
    StartSpeechRecognisation event,
    Emitter<UnityScreenState> emit,
  ) async {
    recognizedWord = "";
    lastResponse = "";
    currentResponse = '';
    previousResult = ""; // Store the previous result
    newTextToProcess = "";
    emit(const StartSpeechToText(
      isTextToSpeechStart: true,
      isRecorderButtonDisabled: true,
      isVideoButtonDisabled: true,
      isDictionaryButtonDisabled: true,
      isTextFormFieldDisabled: true,
    ));
    if (Platform.isIOS) {
      // Await initialization
      bool initialized = await event.speechToText.initialize(
        onError: (errorNotification) {
          recognizedWord = "";
        },
        onStatus: (status) async {
          if (Platform.isIOS) {
            _speechTimer = Timer.periodic(const Duration(seconds: 5), (_) {
              if (status == "listening" &&
                  recognizedWord.isNotEmpty &&
                  state.isTextToSpeechStart) {
                if (recognizedWord.isEmpty) {
                  recognizedWord = '';
                }
                _processLatestText(recognizedWord);
              }
            });
          } else {
            if (status == "done" && state.isTextToSpeechStart) {
              add(RestartSpeechRecognisation(words: recognizedWord));
              await startListing(event.speechToText, emit);
              recognizedWord = '';
            }
          }
        },
      );

      if (initialized) {
        await startListing(event.speechToText, emit);
      } else {
        emit(const StartSpeechToText(isTextToSpeechStart: false));
        emit(const UnityScreenError(message: "Permission Denied"));
      }
    } else {
      final status = await _reqAudioPermission();
      if (status == PermissionStatus.granted) {
        _startListening();
      }
    }
  }

  Future<void> _setAPIKey() async {
    try {
      final api = await _aslFlutterInputPlugin
          .setAPIKey("AIzaSyCizaPQJGhorO8m00L3uBuJzX3H5hVm_2c");
      print("_setAPIKey => $api");
    } catch (e) {
      print("Error =>  _setAPIKey => $e");
    }
  }

  Future<PermissionStatus> _reqAudioPermission() async {
    final status = await Permission.microphone.request();
    return status;
  }

  Future<void> _startListening() async {
    try {
      await _setAPIKey();
      // await _aslFlutterInputPlugin.setCurrentLanguage('ar-SA');
      await _aslFlutterInputPlugin.initAudioTranscription();
      await _startTimer();
    } on Exception catch (e) {
      debugPrint('[Exception][_startListening]: $e');
    }
  }

  String normalizeText(String text) {
    // Normalize the text to remove diacritics and standardize Arabic forms
    return text.replaceAll(
        RegExp(r'[\u064B-\u065F\u0610-\u061A\u06D6-\u06ED]'), '');
  }

  // The full previously recognized text

  String getNewText(String currentResult) {
    int divergencePoint = findDivergencePoint(previousResult, currentResult);

    // Extract the truly new text after the overlap
    newTextToProcess = currentResult.substring(divergencePoint);

    // Update previousResult with the current result
    previousResult = currentResult;
    return newTextToProcess;
  }

  int findDivergencePoint(String previous, String current) {
    // Use a sliding window approach to find the longest overlap
    for (int i = 0; i < previous.length; i++) {
      String suffix = previous.substring(i);
      if (current.startsWith(suffix)) {
        return suffix.length; // Found overlap
      }
    }
    return 0; // No overlap, process the entire string as new
  }

  void _handleLatestText(String text) {
    // Implement your processing logic here
    print('Processing latest part: $text');
  }

  void _processLatestText(String newText) {
    if (newText.isNotEmpty && newText != lastProcessedText) {
      String latestPart = '';

      // Find the index where the new text differs from the old text
      int diffIndex = 0;
      while (diffIndex < newText.length &&
          diffIndex < lastProcessedText.length &&
          newText[diffIndex] == lastProcessedText[diffIndex]) {
        diffIndex++;
      }

      // Extract only the new part of the text
      latestPart = newText.substring(diffIndex);

      print('Current Response => $newText');
      print('New text to process: $latestPart');

      // Process the latest part of the text
      _handleLatestText(latestPart);

      // Update the last processed text
      lastProcessedText = newText;
      add(RestartSpeechRecognisation(words: latestPart));
    }
  }

  _startTimer() async {
    _androidSpeechTimer = Timer.periodic(const Duration(seconds: 5), (_) async {
      recognizedWord = await _aslFlutterInputPlugin.fetchTranscribedText();
      print("Current Response =>recognizedWord $recognizedWord ");
      if (recognizedWord.contains('؟')) {
        recognizedWord = recognizedWord.replaceAll('؟', '');
      }
      if (recognizedWord.isEmpty) {
        print("No transcription received.");
        recognizedWord = ''; // Skip processing if no text was recognized
      } else {
        // print("New text to process: ${_processLatestText(recognizedWord)}");
        if (lastUpdatedText != recognizedWord) {
          lastUpdatedText = recognizedWord;
          noResponseCount = 0;
        } else {
          noResponseCount = noResponseCount + 1;
          if (noResponseCount >= 10) {
            isAnimating = false;
          }
        }
        _processLatestText(recognizedWord);
      }
    });
  }

  String getDifference(String first, String second) {
    if (second.startsWith(first)) {
      return second.substring(first.length).trim();
    } else {
      return second;
    }
  }

  Future<void> startListing(
    SpeechToText speech,
    Emitter<UnityScreenState> emit,
  ) async {
    await speech.listen(
      onResult: (res) {
        print("res=> ${res.recognizedWords}");
        recognizedWord = res.recognizedWords;
      },
      localeId: "ar-SA",
    );
  }

  Future<void> startSpeechToTextProcessing(
    RestartSpeechRecognisation event,
    Emitter<UnityScreenState> emit,
  ) async {
    if (event.words.isNotEmpty) {
      final texts = await textConversionService.getTranscribedText(event.words);

      if (texts.isNotEmpty) {
        for (var textItem in texts) {
          final rootWord = textItem.root;
          final speechWord = textItem.word;
          speechTexts.add(rootWord);
          speechWords.add(speechWord);
          print("startSpeechToTextProcessing =>speech $speechTexts");
          print("startSpeechToTextProcessing =>speechWord $speechWords");
          if (!isAnimating) {
            for (var item in speechTexts[speechTextIndex]) {
              rootWords.add(item);
              print("startSpeechToTextProcessing =>rootWords $rootWords");
              if (item.contains(',')) {
                final splitWords = item.split(',');

                for (int i = 0; i < splitWords.length; i++) {
                  var word = splitWords[i];
                  print("speechTextItem =>$word");
                  await Future.delayed(const Duration(milliseconds: 150), () {
                    emit(SendMessagesToUnity(
                      message: {'root': word, 'word': textItem.word.toString()},
                      currentAnimation: state.currentAnimation,
                      isTextToSpeechStart: state.isTextToSpeechStart,
                      isVideoButtonDisabled: state.isVideoButtonDisabled,
                      isRecorderButtonDisabled: state.isRecorderButtonDisabled,
                      isDictionaryButtonDisabled:
                          state.isDictionaryButtonDisabled,
                      isTextFormFieldDisabled: state.isTextFormFieldDisabled,
                    ));
                  });
                }
              } else {
                emit(SendMessagesToUnity(
                  message: {'root': item, 'word': textItem.word.toString()},
                  currentAnimation: state.currentAnimation,
                  isTextToSpeechStart: state.isTextToSpeechStart,
                  isVideoButtonDisabled: state.isVideoButtonDisabled,
                  isRecorderButtonDisabled: state.isRecorderButtonDisabled,
                  isDictionaryButtonDisabled: state.isDictionaryButtonDisabled,
                  isTextFormFieldDisabled: state.isTextFormFieldDisabled,
                ));
              }
            }
          }
        }
      } else {
        if (Platform.isIOS) {
          _speechTimer?.cancel();
        } else {
          _androidSpeechTimer?.cancel();
        }
        emit(const UnityScreenError(
          message: "Invalid Text",
        ));
      }
    }
  }

  void onStopRecognisation(
    StopSpeechRecognisation event,
    Emitter<UnityScreenState> emit,
  ) {
    if (Platform.isIOS) {
      event.speechToText.stop();
      _speechTimer?.cancel;
      recognizedWord = '';
      lastResponse = "";
    } else {
      _aslFlutterInputPlugin.stopListening();
      _androidSpeechTimer?.cancel();
      recognizedWord = '';
      lastResponse = "";
    }
    emit(const StartSpeechToText(isTextToSpeechStart: false));
    speechTexts.clear();
    speechWords.clear();
    speechTextIndex = 0;
    isAnimating = false;
    noResponseCount = 0;
    lastUpdatedText = "";
  }

  void onUpdateDictionary(
      DictionarySearch event, Emitter<UnityScreenState> emit) {
    if (event.value.isEmpty) {
      _suggestions = dictionary.keys.toList();
      emit(UpdateDictionarySearch(
        dictionaryWords: _suggestions,
        isVisible: state.isVisible,
        isDictionaryOverlay: state.isDictionaryOverlay,
      ));
    } else {
      _suggestions = dictionary.keys
          .where(
              (key) => key.toLowerCase().startsWith(event.value.toLowerCase()))
          .toList();
      emit(UpdateDictionarySearch(
        dictionaryWords: _suggestions,
        isVisible: state.isVisible,
        isDictionaryOverlay: state.isDictionaryOverlay,
      ));
    }
  }

  void onDictionaryItemSelected(
      DictionaryItemSelected event, Emitter<UnityScreenState> emit) {
    if (event.value.isNotEmpty) {
      add(ToggleVisibility(isVisible: false));

      emit(SendMessagesToUnity(
        message: {'root': event.value, 'word': event.value},
        currentAnimation: state.currentAnimation,
        isTextToSpeechStart: state.isTextToSpeechStart,
        isVideoButtonDisabled: state.isVideoButtonDisabled,
        isRecorderButtonDisabled: state.isRecorderButtonDisabled,
        isDictionaryButtonDisabled: state.isDictionaryButtonDisabled,
        isTextFormFieldDisabled: state.isTextFormFieldDisabled,
      ));
      add(DictionarySearch(value: ""));
      event.controller.clear();
    }
  }

  void onUpdateSliderValue(
      UpdateSliderValue event, Emitter<UnityScreenState> emit) {
    emit(SliderValueUpdated(
      sliderValue: event.value,
      isTextToSpeechStart: state.isTextToSpeechStart,
      isRecorderButtonDisabled: state.isRecorderButtonDisabled,
      isVideoButtonDisabled: state.isVideoButtonDisabled,
      isSpeechButtonDisabled: state.isSpeechButtonDisabled,
      isDictionaryButtonDisabled: state.isDictionaryButtonDisabled,
      isTextFormFieldDisabled: state.isTextFormFieldDisabled,
    ));
  }

  FutureOr<void> onUpdateAlertStatus(
      UpdateAlertStatus event, Emitter<UnityScreenState> emit) {
    isUpdateAlertShown = event.status;
  }

  @override
  Future<void> close() {
    _timer?.cancel();
    _speechTimer?.cancel();
    _androidSpeechTimer?.cancel();
    return super.close();
  }

  FutureOr<void> onUpdateQueueMessage(
      UpdateQueueMessage event, Emitter<UnityScreenState> emit) {
    emit(UpdateQueueTextWidget(
      isQueueTextShown: event.status,
      isVisible: state.isVisible,
      isDictionaryOverlay: state.isDictionaryOverlay,
      isDataLoading: state.isDataLoading,
    ));
  }

  Future<FutureOr<void>> onSendUpdatedSpeech(
      SendUpdatedSpeech event, Emitter<UnityScreenState> emit) async {
    if (speechTexts.length - 1 >= event.index) {
      final speechList = speechTexts[event.index];
      for (var item in speechList) {
        rootWords.add(item);
        print(
            "onSendUpdatedSpeech =>$speechTexts => $rootWords => ${event.index}");
        if (item.contains(',')) {
          final splitWords = item.split(',');

          for (int i = 0; i < splitWords.length; i++) {
            var word = splitWords[i];
            await Future.delayed(const Duration(milliseconds: 150), () {
              emit(SendMessagesToUnity(
                  message: {
                    'root': word.trim(),
                    'word': speechWords[event.index].toString()
                  },
                  currentAnimation: state.currentAnimation,
                  isTextToSpeechStart: state.isTextToSpeechStart,
                  isVideoButtonDisabled: state.isVideoButtonDisabled,
                  isRecorderButtonDisabled: state.isRecorderButtonDisabled,
                  isDictionaryButtonDisabled: state.isDictionaryButtonDisabled,
                  isTextFormFieldDisabled: state.isTextFormFieldDisabled));
            });
          }
        } else {
          emit(SendMessagesToUnity(
            message: {
              'root': item.trim(),
              'word': speechWords[event.index].toString()
            },
            currentAnimation: state.currentAnimation,
            isTextToSpeechStart: state.isTextToSpeechStart,
            isVideoButtonDisabled: state.isVideoButtonDisabled,
            isRecorderButtonDisabled: state.isRecorderButtonDisabled,
            isDictionaryButtonDisabled: state.isDictionaryButtonDisabled,
            isTextFormFieldDisabled: state.isTextFormFieldDisabled,
          ));
        }
      }
    }
  }
}
