part of 'speech_transcript_bloc.dart';

@immutable
sealed class SpeechTranscriptEvent {}

class StartRecording extends SpeechTranscriptEvent {
  final bool isRecordingEnabled;
  final RecorderController controller;
  final String path;

  StartRecording(
      {required this.isRecordingEnabled,
      required this.controller,
      required this.path});
}

class StopRecording extends SpeechTranscriptEvent {
  final bool isRecordingEnabled;
  final RecorderController controller;
  final String path;

  StopRecording(
      {required this.isRecordingEnabled,
      required this.controller,
      required this.path});
}

class ResetSpeechTranscription extends SpeechTranscriptEvent {
  final RecorderController controller;

  ResetSpeechTranscription({required this.controller});
}

class TranslateButtonTapped extends SpeechTranscriptEvent {
  final RecorderController controller;
  final String path;

  TranslateButtonTapped({required this.controller, required this.path});
}

class UpdateCurrentAnimationText extends SpeechTranscriptEvent {
  final String animationText;

  UpdateCurrentAnimationText({required this.animationText});
}

class StartSpeechRecognition extends SpeechTranscriptEvent {
  final SpeechToText speechToText;

  StartSpeechRecognition({required this.speechToText});
}

class StopSpeechRecognition extends SpeechTranscriptEvent {
  final SpeechToText speechToText;

  StopSpeechRecognition({required this.speechToText});
}

class RestartSpeechRecognition extends SpeechTranscriptEvent {
  final String words;

  RestartSpeechRecognition({required this.words});
}

class UploadFileForTranscription extends SpeechTranscriptEvent {
  final Uint8List fileBytes;
  final String fileName;
  final ValueNotifier<double> progressNotifier;

  UploadFileForTranscription(
      {required this.fileBytes,
      required this.fileName,
      required this.progressNotifier});
}

class StopAllAnimationProcesses extends SpeechTranscriptEvent {
  StopAllAnimationProcesses();
}

class UpdateAnimationSpeed extends SpeechTranscriptEvent {
  final double value;

  UpdateAnimationSpeed({required this.value});
}

class UpdateSliderValue extends SpeechTranscriptEvent {
  final double value;

  UpdateSliderValue({required this.value});
}

class ReplayTranscription extends SpeechTranscriptEvent {}

class ReplayRecording extends SpeechTranscriptEvent {}

class ReplayFileUpload extends SpeechTranscriptEvent {}

class SendUpdatedSpeech extends SpeechTranscriptEvent {
  final int index;

  SendUpdatedSpeech({required this.index});
}

class UploadChunkData extends SpeechTranscriptEvent {
  final PlatformFile file;
  final ValueNotifier<double> progressNotifier;
  final String sourceType;

  UploadChunkData({
    required this.file,
    required this.progressNotifier,
    required this.sourceType,
  });
}

class SendAudioSegment extends SpeechTranscriptEvent {
  final int index;

  SendAudioSegment({required this.index});
}

class SendSessionAlert extends SpeechTranscriptEvent {
  final int alertCount;
  SendSessionAlert({required this.alertCount});
}
