// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'speech_transcript_bloc.dart';

@immutable
abstract class SpeechTranscriptState extends Equatable {
  final String currentAnimation;
  final bool isRecordingEnabled;
  final bool isTranslateButtonDisabled;
  final bool isDataLoading;
  final double animationSpeed;
  final bool isRecordingStopped;

  const SpeechTranscriptState({
    this.currentAnimation = '',
    this.isRecordingEnabled = false,
    this.isTranslateButtonDisabled = false,
    this.isDataLoading = false,
    this.animationSpeed = 1.0,
    this.isRecordingStopped = false,
  });

  @override
  List<Object?> get props => [
        currentAnimation,
        isRecordingEnabled,
        isTranslateButtonDisabled,
        isDataLoading,
        isRecordingStopped,
        animationSpeed,
      ];
}

/// Initial state
class SpeechTranscriptInitial extends SpeechTranscriptState {
  const SpeechTranscriptInitial({
    super.currentAnimation,
    super.isRecordingEnabled,
    super.isTranslateButtonDisabled,
    super.isDataLoading,
    super.animationSpeed,
  });
}

class StartRecordingState extends SpeechTranscriptState {
  const StartRecordingState(
      {super.currentAnimation,
      super.isRecordingEnabled,
      super.isTranslateButtonDisabled,
      super.isDataLoading,
      super.animationSpeed,
      super.isRecordingStopped});
}

class SpeechScreenError extends SpeechTranscriptState {
  final String message;
  const SpeechScreenError({
    required this.message,
    super.currentAnimation,
    super.isRecordingEnabled,
    super.isTranslateButtonDisabled,
    super.isDataLoading,
    super.animationSpeed,
    super.isRecordingStopped,
  });
  @override
  List<Object> get props => [message];
}

class StopRecordingState extends SpeechTranscriptState {
  const StopRecordingState(
      {super.currentAnimation,
      super.isRecordingEnabled,
      super.isTranslateButtonDisabled,
      super.isDataLoading,
      super.animationSpeed,
      super.isRecordingStopped});
}

class SendMessagesToSpeechScreen extends SpeechTranscriptState {
  final Map<String, dynamic> message;
  const SendMessagesToSpeechScreen({
    required this.message,
    super.currentAnimation,
    super.isRecordingEnabled,
    super.isTranslateButtonDisabled,
    super.isDataLoading,
    super.animationSpeed,
    super.isRecordingStopped,
  });
  @override
  List<Object> get props => [message];
}

class UpdateCurrentAnimation extends SpeechTranscriptState {
  const UpdateCurrentAnimation({
    super.currentAnimation,
    super.isRecordingEnabled,
    super.isTranslateButtonDisabled,
    super.isDataLoading,
    super.animationSpeed,
    super.isRecordingStopped,
  });

  @override
  List<Object?> get props => [
        currentAnimation,
        isRecordingEnabled,
        isTranslateButtonDisabled,
        isRecordingStopped,
        isDataLoading,
        animationSpeed,
      ];
}

class StartSpeechRecognitionState extends SpeechTranscriptState {
  const StartSpeechRecognitionState({
    super.currentAnimation,
    super.isRecordingEnabled,
    super.isTranslateButtonDisabled,
    super.isDataLoading,
    super.animationSpeed,
    super.isRecordingStopped,
  });
}

class StopSpeechRecognitionState extends SpeechTranscriptState {
  const StopSpeechRecognitionState({
    super.currentAnimation,
    super.isRecordingEnabled,
    super.isTranslateButtonDisabled,
    super.isDataLoading,
    super.animationSpeed,
    super.isRecordingStopped,
  });
}

class FileUploadLoadingState extends SpeechTranscriptState {
  const FileUploadLoadingState({
    super.currentAnimation,
    super.isRecordingEnabled,
    super.isTranslateButtonDisabled,
    super.isDataLoading = true,
    super.animationSpeed,
    super.isRecordingStopped,
  });
}

// Add a new state for animation speed updates
class AnimationSpeedUpdated extends SpeechTranscriptState {
  const AnimationSpeedUpdated({
    required double animationSpeed,
    super.currentAnimation,
    super.isRecordingEnabled,
    super.isTranslateButtonDisabled,
    super.isDataLoading,
    super.isRecordingStopped,
  }) : super(animationSpeed: animationSpeed);

  @override
  List<Object?> get props => [
        animationSpeed,
        currentAnimation,
        isRecordingEnabled,
        isTranslateButtonDisabled,
        isDataLoading,
        isRecordingStopped,
      ];
}

class SliderValueUpdated extends SpeechTranscriptState {
  final double sliderValue;

  const SliderValueUpdated({
    required this.sliderValue,
    super.currentAnimation,
    super.isRecordingEnabled,
    super.isTranslateButtonDisabled,
    super.isDataLoading,
    super.animationSpeed,
    super.isRecordingStopped,
  });

  @override
  List<Object?> get props => [
        sliderValue,
        currentAnimation,
        isRecordingEnabled,
        isTranslateButtonDisabled,
        isDataLoading,
        animationSpeed,
        isRecordingStopped,
      ];
}

class ShowSessionAlert extends SpeechTranscriptState {
  int alertCount;
  ShowSessionAlert({
    required this.alertCount,
  });
}
