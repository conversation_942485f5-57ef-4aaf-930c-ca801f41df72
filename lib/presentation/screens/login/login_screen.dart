import 'package:arabic_sign_language/bloc/auth/auth_bloc.dart';
import 'package:arabic_sign_language/bloc/language/language_bloc.dart';
import 'package:arabic_sign_language/bloc/user/user_bloc.dart';
import 'package:arabic_sign_language/bloc/user/user_event.dart';
import 'package:arabic_sign_language/common/router.dart';
import 'package:arabic_sign_language/presentation/core/constants.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:arabic_sign_language/presentation/widgets/custom_gradient_button.dart';
import 'package:arabic_sign_language/presentation/widgets/custom_transparent_button.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'dart:ui' as ui;
import 'package:shared_preferences/shared_preferences.dart';

import '../../../bloc/user/user_state.dart';
import '../../../data/service/device_info_service.dart';

class LoginScreen extends StatefulWidget {
  final bool sessionExpired;
  const LoginScreen({super.key, this.sessionExpired = false});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _emailFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();
  bool _isPasswordVisible = false;
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: ['email', 'profile'],
  );

  Map<String, dynamic>? deviceInfo;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    super.dispose();
  }

  // Validation methods
  String? _validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Email is required';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value.trim())) {
      return 'Please enter a valid email address';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    if (value.length < 6) {
      return 'Password must be at least 6 characters';
    }
    return null;
  }

  // Method to clear focus and hide keyboard
  void _clearFocus() {
    _emailFocusNode.unfocus();
    _passwordFocusNode.unfocus();
    FocusScope.of(context).unfocus();
  }

  // Step 1: Handle Google Sign-In button tap
  Future<void> _handleGoogleSignIn() async {
    try {
      _clearFocus();

      print('Starting Google Sign-In process...');

      // Check if Firebase is initialized
      if (_auth.app.options.projectId.isEmpty) {
        print(
            'Firebase not configured properly. Please add real google-services.json');
        throw Exception(
            'Firebase not configured. Please set up Firebase Console and add google-services.json');
      }

      // Force sign out first to ensure we get the account picker
      await _googleSignIn.signOut();

      // Step 1: Trigger the Google Sign-In flow
      print('Triggering Google Sign-In flow...');
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        // User cancelled the sign-in
        print('User cancelled Google Sign-In');
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Google Sign-In was cancelled'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      print('Google user obtained: ${googleUser.email}');

      // Step 2: Obtain the auth details from the request
      print('Getting authentication details...');
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      if (googleAuth.accessToken == null || googleAuth.idToken == null) {
        throw Exception('Failed to get Google authentication tokens');
      }

      print('Google auth tokens obtained');

      // Step 3: Create a new credential for Firebase
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      print('Firebase credential created');

      // Step 4: Sign in to Firebase with the Google credential
      print('Signing in to Firebase...');
      final UserCredential userCredential =
          await _auth.signInWithCredential(credential);

      if (userCredential.user == null) {
        throw Exception('Failed to sign in to Firebase');
      }

      print(
          'Successfully signed in with Google: ${userCredential.user?.displayName}');
      print('User email: ${userCredential.user?.email}');
      print('User ID: ${userCredential.user?.uid}');

      if (!mounted) return;

      // Step 5: Send UserCredential to backend via bloc
      print('Sending UserCredential to backend...');
      context
          .read<AuthBloc>()
          .add(GoogleSignInRequested(userCredential: userCredential));
    } catch (error) {
      print('Error signing in with Google: $error');

      if (!mounted) return;

      String errorMessage = 'Google Sign-In failed';

      // Handle specific error types
      if (error.toString().contains('ApiException: 10')) {
        errorMessage =
            'Google Sign-In configuration error. Please check SHA-1 fingerprint and package name in Firebase Console.';
      } else if (error.toString().contains('network_error')) {
        errorMessage = 'Network error. Please check your internet connection.';
      } else if (error.toString().contains('sign_in_canceled')) {
        errorMessage = 'Sign-in was cancelled.';
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(errorMessage),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
        ),
      );
    }
  }

  Future<Map<String, dynamic>?> fetchDeviceInfo() async {
    DeviceInfoService deviceInfoService = DeviceInfoService();

    final info = await deviceInfoService.getDeviceInfo();
    return info;
  }

  Future<void> _loadDeviceInfo() async {
    final info = await DeviceInfoService().getDeviceInfo();
    if (!mounted) return;
    setState(() => deviceInfo = info);
  }

  void _showSessionExpiredMessage() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.sessionExpired) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Your session has expired. Please log in again.'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 5),
          ),
        );
      }
    });
  }

  @override
  void initState() {
    super.initState();
    _loadDeviceInfo();
    _showSessionExpiredMessage();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: BlocListener<AuthBloc, AuthState>(
        listener: (context, state) {
          if (state is AuthLoading) {
            // Show loading indicator
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context) => const Center(
                child: CircularProgressIndicator(),
              ),
            );
          } else if (state is AuthAuthenticated) {
            // Close loading dialog if open
            if (Navigator.canPop(context)) {
              Navigator.pop(context);
            }

            // Fetch user profile
            context.read<UserBloc>().add(FetchUserProfile());

            // Listen to UserBloc state changes to get the email after profile is fetched
            context.read<UserBloc>().stream.listen((userState) {
              if (userState is UserLoaded && mounted) {
                // Check if widget is still mounted
                // Get user email from loaded profile
                final userEmail = userState.user.email;

                // Check if this email has completed onboarding before
                SharedPreferences.getInstance().then((prefs) {
                  // Get the list of emails that completed onboarding
                  final List<String> completedOnboardingEmails =
                      prefs.getStringList(kEYCompletedOnboardingEmails) ?? [];

                  if (mounted) {
                    // Check again before navigation
                    if (completedOnboardingEmails.contains(userEmail)) {
                      // User already completed onboarding, navigate to HomeScreen

                      context.goNamed(AppRoutes.HOME_ROUTE_NAME);
                    } else {
                      // First time user, navigate to OnboardingScreen
                      context.goNamed(
                        AppRoutes.ONBOARDING_ROUTE_NAME,
                        extra: state.successMessage,
                      );
                    }
                  }
                });
              }
            });
          } else if (state is AuthError) {
            // Close loading dialog if open
            if (Navigator.canPop(context)) {
              Navigator.pop(context);
            }

            _clearFocus();

            // Show error message
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        child: GestureDetector(
          onTap: _clearFocus,
          child: Stack(
            children: [
              // Background image
              Container(
                decoration: const BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(APP_BG),
                    fit: BoxFit.cover,
                  ),
                ),
              ),

              // Foreground content with scroll
              SafeArea(
                child: SingleChildScrollView(
                  reverse: true,
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: Padding(
                    padding: EdgeInsets.only(
                      bottom: MediaQuery.of(context).viewInsets.bottom + 24,
                    ),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 75),
                          Center(
                            child: Image.asset(
                              APP_ICON,
                              width: 80,
                              height: 75,
                            ),
                          ),
                          const SizedBox(height: SPACE25),
                          Text(
                            'login'.tr(),
                            style: Theme.of(context).textTheme.displayLarge,
                          ),
                          const SizedBox(height: SPACE15),
                          Text(
                            'enter_email'.tr(),
                            style: Theme.of(context).textTheme.headlineMedium,
                          ),
                          const SizedBox(height: SPACE12),
                          _buildEmailField(),
                          const SizedBox(height: SPACE15),
                          Text(
                            'enter_password'.tr(),
                            style: Theme.of(context).textTheme.headlineMedium,
                          ),
                          const SizedBox(height: SPACE12),
                          _buildPasswordField(),
                          const SizedBox(height: SPACE25),
                          CustomGradientButton(
                            onPressed: () {
                              if (_formKey.currentState!.validate()) {
                                _clearFocus();
                                context.read<AuthBloc>().add(
                                      LoginRequested(
                                        email: _emailController.text.trim(),
                                        password: _passwordController.text,
                                        deviceInfo: deviceInfo,
                                      ),
                                    );
                              }
                            },
                            label: 'login'.tr(),
                            height: 56,
                          ),
                          Align(
                            alignment: Alignment.centerRight,
                            child: TextButton(
                              onPressed: () {
                                context
                                    .push(AppRoutes.FORGOT_PASSWORD_ROUTE_PATH);
                              },
                              style: TextButton.styleFrom(
                                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              ),
                              child: Text(
                                'forgot_password'.tr(),
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.7),
                                  fontSize: 13,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ),
                          ),
                          Center(
                            child: Text(
                              'or_continue_with'.tr(),
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                          ),
                          const SizedBox(height: SPACE12),
                          Row(
                            children: [
                              Expanded(
                                child: CustomTransparentButton(
                                  onTap: _handleGoogleSignIn,
                                  icon: IC_Google,
                                ),
                              ),
                              // const SizedBox(width: SPACE12),
                              // Expanded(
                              //   child: CustomTransparentButton(
                              //     onTap: () {},
                              //     icon: IC_Apple,
                              //   ),
                              // )
                            ],
                          ),
                          // const SizedBox(height: SPACE12),
                          // Center(
                          //   child: CustomTransparentButton(
                          //     onTap: () {
                          //       Navigator.pushReplacementNamed(
                          //           context, '/home');
                          //     },
                          //     label: 'continue_as_guest'.tr(),
                          //     isWithIcon: false,
                          //   ),
                          // ),
                          const SizedBox(height: SPACE15),
                          Center(
                            child: RichText(
                              textAlign: TextAlign.center,
                              text: TextSpan(
                                text: '${'dont_have_account'.tr()}\n',
                                style:
                                    Theme.of(context).textTheme.headlineMedium,
                                children: [
                                  TextSpan(
                                    text: 'create_new_account'.tr(),
                                    style: Theme.of(context)
                                        .textTheme
                                        .headlineMedium
                                        ?.copyWith(
                                          decoration: TextDecoration.underline,
                                        ),
                                    recognizer: TapGestureRecognizer()
                                      ..onTap = () {
                                        _clearFocus();
                                        context.push(
                                            AppRoutes.SIGNUP_EMAIL_ROUTE_PATH);
                                      },
                                  ),
                                ],
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              Positioned(
                  right: 20,
                  top: 50,
                  child: BlocBuilder<LanguageBloc, LanguageState>(
                      builder: (context, state) {
                    return TextButton(
                      onPressed: () {
                        context.read<LanguageBloc>().add(ToggleLanguage());
                        context.setLocale(state.locale.languageCode == 'en'
                            ? const Locale('ar')
                            : const Locale('en'));
                      },
                      child:
                          Text(state.locale.languageCode == 'en' ? 'AR' : 'EN'),
                    );
                  })),
            ],
          ),
        ),
      ),
    );
  }

  // Form field builders
  Widget _buildEmailField() {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0XFF221B67),
        borderRadius: BorderRadius.circular(12),
        border: _emailController.text.isNotEmpty &&
                _validateEmail(_emailController.text) != null
            ? Border.all(color: Colors.red, width: 1)
            : null,
      ),
      child: TextFormField(
        focusNode: _emailFocusNode,
        controller: _emailController,
        textDirection: ui.TextDirection.ltr,
        style: const TextStyle(
          fontSize: 16,
        ),
        keyboardType: TextInputType.emailAddress,
        decoration: InputDecoration(
          hintText: 'enter_email'.tr(),
          hintStyle: TextStyle(color: Colors.white.withOpacity(0.5)),
          border: InputBorder.none,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
        ),
        validator: _validateEmail,
        textInputAction: TextInputAction.next,
        onChanged: (value) {
          setState(() {}); // Trigger rebuild to update border color
        },
      ),
    );
  }

  Widget _buildPasswordField() {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0XFF221B67),
        borderRadius: BorderRadius.circular(12),
        border: _passwordController.text.isNotEmpty &&
                _validatePassword(_passwordController.text) != null
            ? Border.all(color: Colors.red, width: 1)
            : null,
      ),
      child: TextFormField(
        focusNode: _passwordFocusNode,
        controller: _passwordController,
        textDirection: ui.TextDirection.ltr,
        style: const TextStyle(
          fontSize: 16,
        ),
        obscureText: !_isPasswordVisible,
        decoration: InputDecoration(
          hintText: 'enter_password'.tr(),
          hintStyle: TextStyle(color: Colors.white.withOpacity(0.5)),
          border: InputBorder.none,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
          suffixIcon: IconButton(
            icon: Icon(
              _isPasswordVisible ? Icons.visibility : Icons.visibility_off,
              color: Colors.grey,
            ),
            onPressed: () {
              setState(() {
                _isPasswordVisible = !_isPasswordVisible;
              });
            },
          ),
        ),
        validator: _validatePassword,
        textInputAction: TextInputAction.done,
        onChanged: (value) {
          setState(() {}); // Trigger rebuild to update border color
        },
      ),
    );
  }
}
