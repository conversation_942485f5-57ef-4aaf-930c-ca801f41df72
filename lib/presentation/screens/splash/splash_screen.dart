import 'package:arabic_sign_language/bloc/user/user_bloc.dart';
import 'package:arabic_sign_language/common/router.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../bloc/user/user_event.dart';
import '../../../data/service/device_info_service.dart';
import '../../core/constants.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final userBloc = context.read<UserBloc>();
    Future.delayed(const Duration(seconds: 3), () async {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final isOnBoardingCompleted = prefs.getBool(kEYOnboardingCompleted);
      final isLoggedIn = prefs.getString(kEYAccessToken);
      if (isLoggedIn != null) {
        userBloc.add(FetchUserProfile());
      }
      if (context.mounted) {
        if (isLoggedIn != null) {
          context.go(AppRoutes.HOME_ROUTE_PATH);
        } else {
          context.go(AppRoutes.LOGIN_ROUTE_PATH);
        }
      }
    });
    return Scaffold(
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(image: AssetImage(APP_BG), fit: BoxFit.cover),
        ),
        child: Center(
          child: Image.asset(
            APP_ICON,
            width: 228,
            height: 212,
          ),
        ),
        // color: Colors.black,
      ),
    );
  }
}
