import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:arabic_sign_language/bloc/speechTranscript/speech_transcript_bloc.dart';
import 'package:arabic_sign_language/presentation/core/constants.dart';
import 'package:arabic_sign_language/presentation/screens/home/<USER>';
import 'package:arabic_sign_language/presentation/widgets/custom_gradient_button.dart';

import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_unity_widget/flutter_unity_widget.dart';
import 'package:just_audio/just_audio.dart';
import 'package:path_provider/path_provider.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:easy_localization/easy_localization.dart';

import 'package:arabic_sign_language/presentation/core/unity_controller.dart';

class SpeechTranscriptScreen extends StatefulWidget {
  const SpeechTranscriptScreen({super.key});

  @override
  State<SpeechTranscriptScreen> createState() => _SpeechTranscriptScreenState();
}

class _SpeechTranscriptScreenState extends State<SpeechTranscriptScreen>
    with WidgetsBindingObserver {
  UnityWidgetController? _unityWidgetController;
  RecorderController controller = RecorderController();
  String? path;
  late Directory appDirectory;
  List<String> currentProcessingTexts = [];
  ValueNotifier<int> currentIndex = ValueNotifier<int>(0);
  List<String> messagesFromUnity = [];
  List<String> currentMessageList = [];
  int _selectedTabIndex = 0;
  final List<String> _tabTitles = [
    'record_then_translate',
    'live_translation',
    'upload_file'
  ];

  // Initialize timer as nullable
  Timer? _timer;

  // Speech recognition
  final SpeechToText _speechToText = SpeechToText();
  bool _isListening = false;
  late final SpeechTranscriptBloc speechBloc;

  final ValueNotifier<double> progressNotifier = ValueNotifier(0.0);
  ValueNotifier<double> uploadProgress = ValueNotifier(0.0);
  ValueNotifier<int> popupCountdownNotifier = ValueNotifier<int>(30);

  // Slider value for animation speed control (0.0 to 1.5)
  double sliderValue = 1.0;

  void _initializeControllers() {
    controller = RecorderController()
      ..androidEncoder = AndroidEncoder.aac
      ..androidOutputFormat = AndroidOutputFormat.mpeg4
      ..iosEncoder = IosEncoder.kAudioFormatMPEG4AAC
      ..sampleRate = 44100;
  }

  void _getDir() async {
    appDirectory = await getApplicationDocumentsDirectory();
    path = "${appDirectory.path}/recording.m4a";
    setState(() {});
  }

  void onUnityCreated(controller) {
    _unityWidgetController = controller;
    setCurrentUnityController(controller); // Register this controller globally
  }

  // Communication from  Flutter to Unity
  Future<void> sendMessageToUnity(Map<String, dynamic> message) async {
    message['screen'] = "speechTranscriptScreen";
    String jsonString = json.encode(message);
    print("jsonString =>$jsonString");
    await _unityWidgetController?.postMessage(
        'SaudiCharacter', 'PlaySignAnim', jsonString);
  }

  Future<void> stopAnimations() async {
    if (_unityWidgetController != null) {
      await _unityWidgetController?.postMessage(
          'SaudiCharacter', 'StopAnimations', "");
    }
  }

  Future<void> adjustAnimationSpeed(String value) async {
    try {
      if (_unityWidgetController != null) {
        await _unityWidgetController?.postMessage(
            "SaudiCharacter", 'SetAnimationSpeed', value);
      }
    } catch (e) {
      print("Error adjusting animation speed: $e");
    }
  }

  void pushToSecond(String value) {
    if (!mounted) return; // Add mounted check

    currentProcessingTexts.add(value); // Add the value to `second`.
    print("pushToSecond => $currentProcessingTexts");

    // Combine and clean up the processing texts
    String combined = currentProcessingTexts.join().replaceAll(' ', '');
    print("Combined value: $combined");

    // Check if rootWords list is not empty and currentIndex is valid
    if (!mounted) return; // Add another mounted check before accessing context

    final rootWords = context.read<SpeechTranscriptBloc>().rootWords;
    if (rootWords.isEmpty) {
      print("Warning: rootWords list is empty");
      return;
    }

    if (currentIndex.value >= rootWords.length) {
      print(
          "Warning: currentIndex (${currentIndex.value}) is out of bounds for rootWords (length: ${rootWords.length})");
      return;
    }

    String currentFirstElement =
        rootWords[currentIndex.value].replaceAll(',', '').replaceAll(' ', '');
    print("Current first element: $currentFirstElement ");
    print("Current first element: $combined");

    print("Current first element: $rootWords => ${currentIndex.value}");
    print("Current first element: $value => $currentProcessingTexts");
    print("Current index before update: ${currentIndex.value}");

    // Matching logic
    if (combined == currentFirstElement) {
      currentIndex.value += 1; // Move to the next element in `first`.
      print(
          "Current index after update: ${currentIndex.value}, $currentProcessingTexts");
      currentProcessingTexts.clear(); // Clear for the next processing
      print("Current index after update:  => $currentProcessingTexts");
    } else if (combined.length > currentFirstElement.length) {
      print("Warning: Combined value exceeds the current `first` element.");
    } else {
      print("Currently processing index ${currentIndex.value}");
    }
  }

  // Communication from Unity to Flutter
  Future<void> onUnityMessage(message) async {
    if (!mounted) return;

    if (message.toString().contains('Current playing Animation')) {
      print(
          "onUnityMessage ====> inside current playing animation ${message.toString()}, $currentMessageList, $messagesFromUnity, ${currentIndex.value}");
      List<String> parts = message.split('&');
      Map<String, String> result = {};
      for (var part in parts) {
        List<String> keyValue =
            part.split('=>').map((str) => str.trim()).toList();
        if (keyValue.length == 2) {
          result[keyValue[0]] = keyValue[1];
        }
      }
      // final data = splitWords(message.toString());
      // String combinedData = result.join('  ');
      if (result["screen"] == "speechTranscriptScreen") {
        print(
            "currentAnimation unityScreen => ${result['Current playing Animation']}");
        if (mounted) {
          // Add mounted check before accessing context
          context.read<SpeechTranscriptBloc>().add(UpdateCurrentAnimationText(
              animationText: result['Current playing Animation'] ?? ""));
        }
      }
    } else if (message.toString().contains('switchtoIdle')) {
      Future.delayed(const Duration(seconds: 3), () {
        print("UpdateCurrentAnimationText => 3s");
        if (mounted) {
          // Add mounted check before accessing context
          context
              .read<SpeechTranscriptBloc>()
              .add(UpdateCurrentAnimationText(animationText: ""));
        }
        // context.read<UnityScreenBloc>().isAnimating = false;
        // currentIndex.value = 0;
        // context.read<UnityScreenBloc>().rootWords.clear();
      });
    } else if (message.toString().contains("Current Animation")) {
      currentMessageList.add(message.toString().split("=>").last.trim());
      print("startSpeechToTextProcessing =>c$currentMessageList");

      // Only call pushToSecond if rootWords is not empty
      if (mounted) {
        // Add mounted check before accessing context
        final rootWords = context.read<SpeechTranscriptBloc>().rootWords;
        if (rootWords.isNotEmpty) {
          pushToSecond(message.toString().split("=>").last);
        } else {
          print("Warning: Skipping pushToSecond because rootWords is empty");
        }
      }

      if (messagesFromUnity.length == currentMessageList.length && mounted) {
        currentIndex.value = 0;
        messagesFromUnity.clear();
        currentMessageList.clear();
        if (mounted) {
          // Add mounted check before accessing context
          context.read<SpeechTranscriptBloc>().rootWords.clear();
          context
              .read<SpeechTranscriptBloc>()
              .add(UpdateCurrentAnimationText(animationText: ""));
          print(
              "onUnityMessage => ${context.read<SpeechTranscriptBloc>().speechTexts}");
          if (!context.read<SpeechTranscriptBloc>().isChunkDataProcessing) {
            context.read<SpeechTranscriptBloc>().speechTextIndex++;
            await Future.delayed(
              const Duration(milliseconds: 250),
              () {
                print(
                    "speechTextIndex => ${context.read<SpeechTranscriptBloc>().speechTextIndex}");
                if (context.read<SpeechTranscriptBloc>().speechTexts.length -
                        1 >=
                    context.read<SpeechTranscriptBloc>().speechTextIndex) {
                  context.read<SpeechTranscriptBloc>().add(SendUpdatedSpeech(
                      index: context
                          .read<SpeechTranscriptBloc>()
                          .speechTextIndex));
                } else {
                  _timer = Timer.periodic(const Duration(seconds: 5), (timer) {
                    if (context
                            .read<SpeechTranscriptBloc>()
                            .speechTexts
                            .length >
                        context.read<SpeechTranscriptBloc>().speechTextIndex) {
                      // context.read<SpeechTranscriptBloc>().speechTextIndex++;
                      context.read<SpeechTranscriptBloc>().add(
                          SendUpdatedSpeech(
                              index: context
                                  .read<SpeechTranscriptBloc>()
                                  .speechTextIndex));

                      timer.cancel();
                    }
                  });

                  // Future.delayed(const Duration(seconds: 2), () {
                  //   context.read<SpeechTranscriptBloc>().add(SendUpdatedSpeech(
                  //       index: context
                  //           .read<SpeechTranscriptBloc>()
                  //           .speechTextIndex));
                  //   // print(
                  //   //     "onUnityMessage 1 =>${context.read<UnityScreenBloc>().speechItems}, ${context.read<UnityScreenBloc>().speechTextIndex}");
                  // });
                }
              },
            );
          } else {
            Future.delayed(const Duration(seconds: 1), () {
              context.read<SpeechTranscriptBloc>().audioSegmentIndex++;
              context.read<SpeechTranscriptBloc>().add(SendAudioSegment(
                  index:
                      context.read<SpeechTranscriptBloc>().audioSegmentIndex));
            });
          }
        }
      }
    }
  }

  List<String> splitWords(String message) {
    const prefix = "Current playing Animation =>";
    if (message.startsWith(prefix)) {
      String words = message.substring(prefix.length).trim();
      return words.split(' ');
    }
    return [];
  }

  Future<bool> isDurationWithinLimit(String filePath,
      {required int maxSeconds}) async {
    final player = AudioPlayer();
    try {
      await player.setFilePath(filePath);
      final duration = player.duration;
      await player.dispose();

      if (duration != null && duration.inSeconds <= maxSeconds) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      print("Error checking duration: $e");
      await player.dispose();
      return false;
    }
  }

  void _showContinuePopup() {
    if (speechBloc.isPopupVisible) return; // Prevent multiple dialogs
    speechBloc.isPopupVisible = true;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (bContext) {
        return StatefulBuilder(
          builder: (sContext, setState) {
            _startPopupCountdown(sContext);

            return AlertDialog(
              title: const Text('Continue Listening?'),
              content: ValueListenableBuilder(
                  valueListenable: popupCountdownNotifier,
                  builder: (context, value, _) {
                    return Text(
                        'Do you want to continue listening? or the session will stop within 00:$value seconds. ');
                  }),
              actions: [
                TextButton(
                  onPressed: () {
                    _dismissPopup(
                      sContext,
                      resetTimer: true,
                    );
                  },
                  child: const Text('Continue'),
                ),
                TextButton(
                  onPressed: () {
                    _dismissPopup(sContext, stopListening: true);
                  },
                  child: const Text('Stop'),
                ),
              ],
            );
          },
        );
      },
    ).then((_) {
      // Ensure flag and timer cleanup when dialog is dismissed manually (back button)
      speechBloc.isPopupVisible = false;
      speechBloc.popupCountdownTimer?.cancel();
    });
  }

  void _startPopupCountdown(BuildContext context) {
    popupCountdownNotifier.value = 30;
    speechBloc.popupCountdownTimer?.cancel();
    speechBloc.popupCountdownTimer =
        Timer.periodic(const Duration(seconds: 1), (timer) {
      if (popupCountdownNotifier.value <= 1) {
        timer.cancel();
        _dismissPopup(context, stopListening: true);
      } else {
        popupCountdownNotifier.value--;
      }
    });
  }

  void _dismissPopup(BuildContext context,
      {bool resetTimer = false, bool stopListening = false}) {
    if (speechBloc.isPopupVisible) {
      speechBloc.isPopupVisible = false;
      speechBloc.popupCountdownTimer?.cancel();
      speechBloc.sessionTimer?.cancel();

      Navigator.of(context, rootNavigator: true).maybePop();

      if (resetTimer) {
        // speechBloc.add(ResetSessionTimer()); // Optional: Reset the session timer
        speechBloc.startSessionTimer();
      } else if (stopListening) {
        speechBloc.add(StopAllAnimationProcesses());
        speechBloc.add(StopSpeechRecognition(speechToText: _speechToText));
        setState(() {
          _isListening = false;
        });
      }
    }
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeControllers();
    speechBloc = context.read<SpeechTranscriptBloc>();
    _getDir();
  }

  @override
  void dispose() {
    // Cancel any pending operations
    speechBloc.add(StopAllAnimationProcesses());
    speechBloc.add(
      StopSpeechRecognition(speechToText: _speechToText),
    );
    // Stop Unity animations
    stopAnimations();
    // Dispose controllers
    controller.dispose();
    currentIndex.dispose();
    // Clear lists
    messagesFromUnity.clear();
    currentMessageList.clear();
    currentProcessingTexts.clear();
    // Dispose Unity controller
    if (_unityWidgetController != null) {
      _unityWidgetController!.dispose();
      _unityWidgetController = null;
    }

    // Clear the global reference if this controller is the current one
    if (currentUnityController == _unityWidgetController) {
      currentUnityController = null;
    }

    _timer?.cancel();
    WidgetsBinding.instance.removeObserver(this);

    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if ((state == AppLifecycleState.inactive ||
            state == AppLifecycleState.paused) &&
        _isListening) {
      speechBloc.add(StopSpeechRecognition(speechToText: _speechToText));
      speechBloc.add(StopAllAnimationProcesses());
      messagesFromUnity.clear();
      currentMessageList.clear();
      currentProcessingTexts.clear();
      currentIndex.value = 0;
      setState(() {
        _isListening = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return BlocConsumer<SpeechTranscriptBloc, SpeechTranscriptState>(
      listener: (context, state) {
        if (state is SendMessagesToSpeechScreen) {
          print("state is SendMessagesToSpeechScreen");
          print("******${state.message}*****");
          messagesFromUnity.add(state.message['root']);
          context.read<SpeechTranscriptBloc>().isAnimating = true;
          sendMessageToUnity(state.message);
        } else if (state is SpeechScreenError) {
          print("state is SpeechScreenError");
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        } else if (state is StopRecordingState) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text("Recording stopped"),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        } else if (state is ShowSessionAlert) {
          _showContinuePopup();
        } else if (state is AnimationSpeedUpdated) {
          // Update Unity animation speed when the animation speed changes
          adjustAnimationSpeed(state.animationSpeed.toString());
        } else if (state is SliderValueUpdated) {
          // Update local slider value and Unity animation speed
          sliderValue = state.sliderValue;
          adjustAnimationSpeed(state.animationSpeed.toString());
        }
      },
      builder: (context, state) => Stack(
        children: [
          Container(
            height: MediaQuery.of(context).size.height,
            width: MediaQuery.of(context).size.width,
            decoration: const BoxDecoration(
              image:
                  DecorationImage(image: AssetImage(APP_BG), fit: BoxFit.cover),
            ),
          ),
          Container(
            padding: const EdgeInsets.fromLTRB(0, kToolbarHeight, 0, 0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header
                _buildHeader(context),
                const SizedBox(height: 15),

                _buildTabBar(),

                const SizedBox(height: 5),

                // Main content
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 20),
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      gradient: LinearGradient(
                        colors: [
                          const Color(0XFF9064FC).withOpacity(0.5),
                          const Color(0XFF1E113A).withOpacity(0.8),
                        ],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      )),
                  child: Column(
                    children: [
                      const SizedBox(height: 40),

                      // Input section based on selected tab
                      _selectedTabIndex == 0
                          ? _buildAudioInput(size, controller)
                          : _selectedTabIndex == 1
                              ? _buildLiveTranslation(size)
                              : _buildUploadOption(size),

                      const SizedBox(height: 30),

                      // Translate button
                      if (_selectedTabIndex == 0) _buildTranslateButton(),

                      if (_selectedTabIndex == 0) const SizedBox(height: 30),

                      // Character avatar
                      _buildCharacterAvatar(context),

                      const SizedBox(height: 40),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Removed the Positioned widget for control buttons since they're now in the character avatar
          Positioned(
            bottom: MediaQuery.sizeOf(context).height * 0.12,
            child: // Status text
                _buildStatusText(state, MediaQuery.sizeOf(context)),
          )
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(30),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: List.generate(
          _tabTitles.length,
          (index) => _buildTabItem(index),
        ),
      ),
    );
  }

  Widget _buildTabItem(int index) {
    final isSelected = _selectedTabIndex == index;

    return Flexible(
      child: GestureDetector(
        onTap: () async {
          final speechBloc = context.read<SpeechTranscriptBloc>();

          speechBloc.add(StopAllAnimationProcesses());
          speechBloc.audioSegmentIndex = 0;
          speechBloc.audioResponseList.clear();
          speechBloc.isChunkDataProcessing = false;
          speechBloc.rootWords.clear();

          messagesFromUnity.clear();
          currentMessageList.clear();
          currentProcessingTexts.clear();
          currentIndex.value = 0;

          await stopAnimations();

          if (_isListening) {
            print(
                "Stopping speech recognition from Live Translation widget...");
            speechBloc.add(StopSpeechRecognition(speechToText: _speechToText));
            setState(() {
              _isListening = false;
            });
          }

          if (!mounted) return;

          setState(() {
            _selectedTabIndex = index;
          });
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color:
                    isSelected ? const Color(0xFF7B6FFF) : Colors.transparent,
                width: 2,
              ),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                LIVE,
                height: 15,
                width: 15,
              ),
              const SizedBox(width: 5),
              Flexible(
                child: Text(
                  _tabTitles[index].tr(),
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: isSelected
                        ? Colors.white
                        : Colors.white.withOpacity(0.6),
                    fontWeight: isSelected ? FontWeight.w400 : FontWeight.w300,
                    fontSize: 12,
                    fontFamily: FONT_FAMILY_INTER,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLiveTranslation(Size size) {
    return GestureDetector(
      onTap: () {
        print(
            "Live Translation widget tapped. Current listening state: $_isListening");
        if (_isListening) {
          print("Stopping speech recognition from Live Translation widget...");
          context.read<SpeechTranscriptBloc>().add(
                StopSpeechRecognition(speechToText: _speechToText),
              );
          setState(() {
            _isListening = false;
          });
        } else {
          print("Starting speech recognition from Live Translation widget...");
          context.read<SpeechTranscriptBloc>().add(
                StartSpeechRecognition(speechToText: _speechToText),
              );
          setState(() {
            _isListening = true;
          });
        }
      },
      child: Container(
        height: MediaQuery.of(context).size.height * 0.05,
        width: MediaQuery.of(context).size.width * 0.9,
        decoration: BoxDecoration(
          color: _isListening ? null : const Color(0XFF221B67),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: _isListening
                ? Colors.red.withOpacity(0.8) // Red border when listening
                : Colors.white.withOpacity(0.3), // Default border
            width: _isListening ? 2 : 1, // Thicker border when listening
          ),
          gradient: const LinearGradient(
            // Default gradient
            colors: [
              purpleMimosa,
              lightRoyalBlue,
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                LIVE,
                height: 25,
                width: 25,
              ),
              const SizedBox(width: 8),
              Text(
                _isListening ? "listening".tr() : "live_translation".tr(),
                style: TextStyle(
                  color: Colors.white.withOpacity(0.9),
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                  fontFamily: FONT_FAMILY,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUploadOption(Size size) {
    return BlocBuilder<SpeechTranscriptBloc, SpeechTranscriptState>(
      builder: (context, state) {
        bool isChunkUploading =
            context.read<SpeechTranscriptBloc>().isChunkUploading;
        return GestureDetector(
          onTap: state.currentAnimation.isNotEmpty
              ? null
              : () async {
                  try {
                    // Use file_picker package to select audio files
                    FilePickerResult? result =
                        await FilePicker.platform.pickFiles(
                      type: FileType.audio,
                      allowMultiple: false,
                    );

                    if (result != null && result.files.isNotEmpty) {
                      // Get the selected file
                      PlatformFile file = result.files.first;

                      if (file.path != null) {
                        final isValid = await isDurationWithinLimit(file.path!,
                            maxSeconds: 1800);

                        if (!isValid) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text(
                                  "please select a file less than 30 minutes long"),
                              duration: Duration(seconds: 2),
                            ),
                          );
                          return;
                        } else {
                          // Check file size (24MB = 24 * 1024 * 1024 bytes = 25,165,824 bytes)
                          const int maxFileSizeInBytes =
                              24 * 1024 * 1024; // 24MB

                          if (file.size > maxFileSizeInBytes) {
                            context.read<SpeechTranscriptBloc>().add(
                                UploadChunkData(
                                    file: file,
                                    progressNotifier: progressNotifier));
                          } else {
                            // Handle the selected audio file (file size is within limit)
                            print('Selected audio file: ${file.name}');
                            print('File path: ${file.path}');
                            print(
                                'File size: ${(file.size / (1024 * 1024)).toStringAsFixed(2)}MB');

                            // Read file bytes and trigger BLoC event for transcription
                            final fileBytes =
                                await File(file.path!).readAsBytes();

                            if (mounted) {
                              if (file.size > maxFileSizeInBytes) {
                                // Show alert dialog for file size limit
                                if (mounted) {
                                  showDialog(
                                    context: context,
                                    builder: (BuildContext context) {
                                      return AlertDialog(
                                        title: const Text(
                                            'File Size Limit Exceeded'),
                                        content: Text(
                                          'The selected file size is ${(file.size / (1024 * 1024)).toStringAsFixed(1)}MB. '
                                          'Please select a file with size up to 24MB only.',
                                        ),
                                        actions: [
                                          TextButton(
                                            onPressed: () {
                                              Navigator.of(context).pop();
                                            },
                                            child: const Text('OK'),
                                          ),
                                        ],
                                      );
                                    },
                                  );
                                }
                                return; // Exit early if file is too large
                              }

                              // context.read<SpeechTranscriptBloc>().add(
                              //       UploadFileForTranscription(
                              //           fileBytes: fileBytes,
                              //           fileName: file.name,
                              //           progressNotifier: uploadProgress),
                              //     );
                            }
                          }
                        }
                      }
                    }
                  } catch (e) {
                    print('Error picking file: $e');
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('error_selecting_audio'.tr()),
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    }
                  }
                },
          child: Container(
            height: MediaQuery.of(context).size.height * 0.08,
            width: MediaQuery.of(context).size.width * 0.9,
            decoration: BoxDecoration(
              color: const Color(0XFF221B67),
              borderRadius: BorderRadius.circular(15),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 25),
                child: state.isDataLoading
                    ? Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          isChunkUploading
                              ? ValueListenableBuilder<double>(
                                  valueListenable: progressNotifier,
                                  builder: (context, value, _) {
                                    return LinearProgressIndicator(
                                        value: value);
                                  },
                                )
                              : ValueListenableBuilder(
                                  valueListenable: uploadProgress,
                                  builder: (context, value, _) {
                                    return LinearProgressIndicator(
                                      backgroundColor:
                                          Colors.white.withOpacity(0.2),
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white.withOpacity(0.8),
                                      ),
                                      value: value,
                                    );
                                  }),
                          const SizedBox(height: 8),
                          Text(
                            "processing_audio_file".tr(),
                            style: TextStyle(
                              color: Colors.white.withOpacity(0.7),
                              fontSize: 12,
                              fontFamily: FONT_FAMILY,
                            ),
                          ),
                        ],
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            "upload_audio_file".tr(),
                            style: TextStyle(
                              color: Colors.white.withOpacity(0.7),
                              fontSize: 14,
                              fontFamily: FONT_FAMILY,
                            ),
                          ),
                          const Spacer(),
                          Image.asset(UPLOAD, height: 25, width: 25),
                        ],
                      ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTranslateButton() {
    return BlocBuilder<SpeechTranscriptBloc, SpeechTranscriptState>(
      builder: (context, state) {
        bool isEnabled = false;

        // Enable button based on tab and state
        if (_selectedTabIndex == 0) {
          isEnabled = state.isRecordingStopped;
        } else if (_selectedTabIndex == 1) {
          // Logic for live translation tab
          isEnabled = true;
        } else if (_selectedTabIndex == 2) {
          // Logic for upload tab
          isEnabled = true;
        }

        return CustomGradientButton(
          gradientColors: isEnabled
              ? [purpleMimosa, lightRoyalBlue]
              : [Colors.grey[900]!, Colors.grey],
          onPressed: isEnabled
              ? () {
                  if (_selectedTabIndex == 0) {
                    context.read<SpeechTranscriptBloc>().add(
                          TranslateButtonTapped(
                              controller: controller, path: path ?? ""),
                        );
                  } else if (_selectedTabIndex == 1) {
                    // Handle live translation
                  } else if (_selectedTabIndex == 2) {
                    // Handle upload
                  }
                }
              : () {},
          label: "translate".tr(),
        );
      },
    );
  }

  Widget _buildAudioInput(Size size, RecorderController controller) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.08,
      width: MediaQuery.of(context).size.width * 0.9,
      decoration: BoxDecoration(
        color: const Color(0XFF221B67),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: BlocBuilder<SpeechTranscriptBloc, SpeechTranscriptState>(
          builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: state.isRecordingEnabled
              ? Row(
                  children: [
                    Expanded(
                      flex: 8,
                      child: AudioWaveforms(
                        size: Size(double.infinity, size.height * 0.08),
                        recorderController: controller,
                        enableGesture: true,
                        waveStyle: const WaveStyle(
                          // showHourInDuration: true,
                          waveColor: Colors.white,
                          // showDurationLabel: true,
                          spacing: 3.0,
                          waveThickness: 1,
                          showBottom: false,
                          extendWaveform: true,
                          showMiddleLine: false,
                        ),
                      ),
                    ),
                    const Spacer(),
                    !state.isRecordingStopped && !state.isDataLoading
                        ? InkWell(
                            onTap: () {
                              context.read<SpeechTranscriptBloc>().add(
                                    StopRecording(
                                        isRecordingEnabled: true,
                                        controller: controller,
                                        path: path ?? ""),
                                  );
                            },
                            child: Container(
                              padding: const EdgeInsets.all(10),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child:
                                  Image.asset(STOP_REC, height: 20, width: 20),
                            ),
                          )
                        : const SizedBox()
                  ],
                )
              : Row(
                  children: [
                    Text(
                      "tap_to_start_recording".tr(),
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.7),
                        fontSize: 14,
                        fontFamily: FONT_FAMILY,
                      ),
                    ),
                    const Spacer(),
                    InkWell(
                      onTap: () {
                        context.read<SpeechTranscriptBloc>().add(
                              StartRecording(
                                isRecordingEnabled: true,
                                controller: controller,
                                path: path ?? "",
                              ),
                            );
                      },
                      child: Container(
                        padding: const EdgeInsets.all(5),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Image.asset(START_REC, height: 25, width: 25),
                      ),
                    )
                  ],
                ),
        );
      }),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Row(
        children: [
          // Back button
          GestureDetector(
              onTap: () async {
                bottomBarIndex.value = 0;

                // Stop all animation processes in BLoC
                context
                    .read<SpeechTranscriptBloc>()
                    .add(StopAllAnimationProcesses());

                // Stop Unity animations
                await stopAnimations();

                if (mounted) {
                  context.read<SpeechTranscriptBloc>().add(
                        ResetSpeechTranscription(controller: controller),
                      );
                }
              },
              child: Image.asset(BACK_BUTTON, height: 25, width: 25)),

          const SizedBox(width: 15),

          // Title
          Text(
            'translate_speech'.tr(),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w600,
              fontFamily: FONT_FAMILY,
            ),
          ),

          const Spacer(),

          // Info button
          GestureDetector(
              onTap: () {}, child: Image.asset(INFO, height: 25, width: 25)),

          const SizedBox(width: 10),

          // Language toggle button
          // BlocBuilder<LanguageBloc, LanguageState>(
          //   builder: (context, state) {
          //     return GestureDetector(
          //       onTap: () {
          //         // Toggle language
          //         context.read<LanguageBloc>().add(ToggleLanguage());

          //         // Set locale based on current language
          //         final newLocale = state.locale.languageCode == 'en'
          //             ? const Locale('ar')
          //             : const Locale('en');

          //         context.setLocale(newLocale);

          //         // Force rebuild by triggering a state change
          //         setState(() {});
          //       },
          //       child: Container(
          //         padding: const EdgeInsets.all(8),
          //         decoration: BoxDecoration(
          //           color: Colors.white.withOpacity(0.1),
          //           borderRadius: BorderRadius.circular(20),
          //         ),
          //         child: Text(
          //           state.locale.languageCode == 'en' ? 'AR' : 'EN',
          //           style: const TextStyle(
          //             color: Colors.white,
          //             fontSize: 14,
          //             fontWeight: FontWeight.w500,
          //           ),
          //         ),
          //       ),
          //     );
          //   },
          // ),
        ],
      ),
    );
  }

  Widget _buildCharacterAvatar(BuildContext context) {
    final recordingList =
        context.watch<SpeechTranscriptBloc>().latsRecordingList;
    final fileUploadList =
        context.watch<SpeechTranscriptBloc>().lastFileUploadList;
    final screenHeight = MediaQuery.sizeOf(context).height;
    return Container(
      height: screenHeight * 0.25, // Increased height for better proportions
      margin: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.white.withOpacity(0.1),
            Colors.white.withOpacity(0.05),
          ],
        ),
      ),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Character image in ClipRRect for rounded corners
          ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: UnityWidget(
              onUnityCreated: onUnityCreated,
              onUnityMessage: onUnityMessage,
            ),
          ),

          // Control buttons positioned to the right
          Positioned(
            right: -25, // Position outside the container
            top: screenHeight * 0.04, // Center vertically
            child: BlocBuilder<SpeechTranscriptBloc, SpeechTranscriptState>(
              builder: (context, state) {
                final animationSpeed = state.animationSpeed;
                // Determine if buttons should be disabled based on animation speed
                final isIncreaseDisabled = animationSpeed >= 2.0;
                final isDecreaseDisabled = animationSpeed <= 0.5;

                return Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(23),
                    color: const Color(0XFF49446C).withOpacity(0.7),
                  ),
                  padding:
                      const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _selectedTabIndex != 1
                          ? _buildControlButton(
                              Icons.refresh,
                              _selectedTabIndex == 0
                                  ? state.currentAnimation.isEmpty &&
                                          recordingList.isNotEmpty
                                      ? () {
                                          context
                                              .read<SpeechTranscriptBloc>()
                                              .add(
                                                ReplayRecording(),
                                              );
                                        }
                                      : null
                                  : _selectedTabIndex == 2
                                      ? state.currentAnimation.isEmpty &&
                                              fileUploadList.isNotEmpty
                                          ? () {
                                              context
                                                  .read<SpeechTranscriptBloc>()
                                                  .add(
                                                    ReplayFileUpload(),
                                                  );
                                            }
                                          : null
                                      : null,
                              _selectedTabIndex == 0
                                  ? state.currentAnimation.isEmpty &&
                                          recordingList.isNotEmpty
                                      ? const Color(0XFF7B6FFF)
                                      : Colors.grey.withOpacity(0.8)
                                  : _selectedTabIndex == 2
                                      ? state.currentAnimation.isEmpty &&
                                              fileUploadList.isNotEmpty
                                          ? const Color(0XFF7B6FFF)
                                          : Colors.grey.withOpacity(0.8)
                                      : Colors.grey.withOpacity(0.8),
                            )
                          : const SizedBox(),
                      _verticalSlider(),
                    ],
                  ),
                );
              },
            ),
          ),
          // Add vertical slider for animation speed control
        ],
      ),
    );
  }

  Widget _buildControlButton(
    IconData icon,
    VoidCallback? onTap,
    Color backgroundColor,
  ) {
    return Material(
      // Circle with your background color.
      color: backgroundColor,
      shape: const CircleBorder(),
      clipBehavior: Clip.antiAlias, // keeps splash inside the circle
      child: InkWell(
        onTap: onTap,
        customBorder: const CircleBorder(), // matches the shape
        splashColor: Colors.white24, // optional – tweak to taste
        highlightColor: Colors.white10, // optional – tweak to taste
        child: SizedBox(
          width: 40,
          height: 40,
          child: Icon(
            icon,
            color: Colors.white,
            size: 18,
          ),
        ),
      ),
    );
  }

  Widget _buildStatusText(SpeechTranscriptState state, Size size) {
    return Container(
      width: MediaQuery.sizeOf(context).width,
      alignment: Alignment.center,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: BlocBuilder<SpeechTranscriptBloc, SpeechTranscriptState>(
        builder: (context, state) {
          List<String> words = state.currentAnimation
              .replaceAll('[', '')
              .replaceAll(']', '')
              .split(', ');
          return SizedBox(
            width: size.width * 0.7,
            child: ValueListenableBuilder(
                valueListenable: currentIndex,
                builder: (context, currentActiveIndex, _) {
                  return Text.rich(
                    TextSpan(
                      children: List<TextSpan>.generate(words.length, (index) {
                        return TextSpan(
                          text:
                              '${words[index]}${index < words.length - 1 ? ' ' : ''}', // Add space between words
                          style: TextStyle(
                            backgroundColor: index == currentActiveIndex
                                ? const Color(0XFF755BFF)
                                : null,
                            color: Colors.white, // Highlighted color
                            fontWeight: FontWeight.bold, // Bold for highlighted
                            fontSize: 18,
                          ),
                        );
                      }),
                    ),
                    textAlign: TextAlign.center,
                  );
                }),
          );
        },
      ),
    );
  }

  Widget _verticalSlider() {
    return Container(
      width: 40,
      margin: const EdgeInsets.symmetric(vertical: 10),
      padding: EdgeInsets.all(5),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
      ),
      height: MediaQuery.of(context).size.height * 0.1,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          // InkWell(
          //   onTap: () {
          //     if (sliderValue < 1.5) {
          //       sliderValue = sliderValue + 0.5;
          //       context
          //           .read<SpeechTranscriptBloc>()
          //           .add(UpdateSliderValue(value: sliderValue));
          //     }
          //   },
          //   child: const Padding(
          //     padding: EdgeInsets.symmetric(vertical: 3),
          //     child: Icon(
          //       Icons.add,
          //       color: Colors.black,
          //     ),
          //   ),
          // ),
          Expanded(
            child: RotatedBox(
              quarterTurns: 3,
              child: Container(
                margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 2),
                decoration: BoxDecoration(
                  color: const Color(0xFFE8E8E8),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: SliderTheme(
                  data: SliderThemeData(
                    overlayShape: SliderComponentShape.noOverlay,
                    thumbShape: const RoundSliderThumbShape(
                      enabledThumbRadius: 15,
                      disabledThumbRadius: 20,
                    ),
                  ),
                  child:
                      BlocBuilder<SpeechTranscriptBloc, SpeechTranscriptState>(
                    builder: (context, state) {
                      if (state is SliderValueUpdated) {
                        sliderValue = state.sliderValue;
                      }
                      return Slider(
                        activeColor: const Color(0xFFE8E8E8),
                        inactiveColor: const Color(0xFFE8E8E8),
                        thumbColor: const Color(0xFF054DA4),
                        value: sliderValue,
                        min: 0,
                        max: 1.5,
                        divisions: 3,
                        onChanged: (val) {
                          context
                              .read<SpeechTranscriptBloc>()
                              .add(UpdateSliderValue(value: val));
                        },
                      );
                    },
                  ),
                ),
              ),
            ),
          ),
          // InkWell(
          //   onTap: () {
          //     if (sliderValue > 0) {
          //       sliderValue = sliderValue - 0.5;
          //       context
          //           .read<SpeechTranscriptBloc>()
          //           .add(UpdateSliderValue(value: sliderValue));
          //     }
          //   },
          //   child: const Icon(
          //     Icons.remove,
          //     color: Colors.black,
          //   ),
          // ),
        ],
      ),
    );
  }
}
