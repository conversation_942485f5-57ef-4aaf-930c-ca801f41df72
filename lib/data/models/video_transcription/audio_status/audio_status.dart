import 'package:arabic_sign_language/data/models/video_transcription/transcription_item.dart';
import 'package:json_annotation/json_annotation.dart';

part 'audio_status.g.dart';

@JsonSerializable()
class AudioStatus {
  bool status;
  @Json<PERSON>ey(name: 'session_id')
  int sessionId;
  List<Data> data;
  @Json<PERSON>ey(name: 'tracking_id')
  String trackingId;

  AudioStatus(
      {required this.status,
      required this.sessionId,
      required this.data,
      required this.trackingId});

  factory AudioStatus.fromJson(Map<String, dynamic> json) {
    return _$AudioStatusFromJson(json);
  }

  Map<String, dynamic> toJson() => _$AudioStatusToJson(this);
}
