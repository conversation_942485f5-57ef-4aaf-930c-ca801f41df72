import 'dart:convert';
import 'dart:io';

import 'package:arabic_sign_language/bloc/UnityScreen/unity_screen_bloc.dart';
import 'package:arabic_sign_language/data/service/base_service.dart';
import 'package:arabic_sign_language/presentation/core/url.dart';
import 'package:arabic_sign_language/data/models/video_transcription/transcription_item.dart';
import 'package:arabic_sign_language/data/models/video_transcription/video_session.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:path_provider/path_provider.dart';
import 'package:youtube_explode_dart/youtube_explode_dart.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';
import 'package:path/path.dart' as p;

import '../../main.dart';
import '../models/video_transcription/audio_session.dart';
import '../models/video_transcription/audio_status/audio_status.dart';

class TranscriptService extends BaseService {
  final yt = YoutubeExplode();

  TranscriptService({Dio? dio})
      : super(
          baseUrl: AUTH_BASE_URL,
          headers: {"Content-Type": "application/json"},
        ) {
    if (dio != null) {
      this.dio = dio;
    }

    // Configure Dio to handle redirects properly
    this.dio.options.followRedirects = true;
    this.dio.options.maxRedirects = 5;
    this.dio.options.validateStatus = (status) {
      return status != null && status >= 200 && status < 500;
    };
  }

  Future<VideoSession> startTranscription(String url) async {
    try {
      final response =
          await dio.post(startTranscript, data: {"youtube_link": url});
      if (response.statusCode == 200 && response.data['status']) {
        final videoSession = VideoSession.fromJson(jsonEncode(response.data));
        return videoSession;
      } else {
        print(
            'Failed to start transcription. Status code: ${response.statusCode}');
        return VideoSession(status: false, videoUrl: "", sessionId: 0);
      }
    } catch (e) {
      print('Error starting transcription: $e');
      return VideoSession(status: false, videoUrl: "", sessionId: 0);
    }
  }

  Future<void> fetchTranscriptWithRetry(
      int sessionId, int startTime, BuildContext context) async {
    bool isCompleted = false;
    int endTime = startTime;
    while (!isCompleted) {
      final transcript = await getTranscript(
        sessionId,
        startTime: endTime,
      );
      if (transcript.status == 'COMPLETED') {
        isCompleted = true;
      } else {
        endTime = transcript.data.last.endTime.toInt();
        await Future.delayed(const Duration(seconds: 3));
      }
    }
  }

  Future<CaptionData> getTranscript(int videoId, {int startTime = 0}) async {
    try {
      final context = navigatorKey.currentContext;
      final response = await dio.post(getCaption, data: {
        "session_id": videoId,
        "start_time": startTime,
      });

      if (response.statusCode == 200) {
        context?.read<UnityScreenBloc>().add(UpdateQueueMessage(status: true));
        if (response.data['status'] ==
            "Video status is QUEUED, Video is in Queue") {
          context
              ?.read<UnityScreenBloc>()
              .add(UpdateQueueMessage(status: true));
          await Future.delayed(const Duration(seconds: 5));
          return getTranscript(
            videoId,
          );
        } else if (response.data['status'] == "Video is being transcribed") {
          await Future.delayed(const Duration(seconds: 5));
          return getTranscript(videoId);
          // Recursive call
        } else if (response.data['status'] ==
            'Analyzed the audio but nothing more to transcribe') {
          return CaptionData(data: [], status: false);
        } else if (response.data['status'] != false) {
          final captions = CaptionData.fromJson(response.data);
          print('getTranscript => ${captions.data}');
          print('getTranscript => ${captions.status}');
          captionList.addAll(captions.data);
          print('getTranscript => ${captionList.length}');
          return captions;
        }
      }

      print('Failed to getCaption. Status code: ${response.statusCode}');
      return CaptionData(data: [], status: false);
    } catch (e) {
      print('Error starting getTranscript: $e');
      return CaptionData(data: [], status: false);
    }
  }

  Future<File> generateAudioFile(String videoUrl) async {
    try {
      var videoInfo = await yt.videos.get(videoUrl);
      var manifest = await yt.videos.streamsClient.getManifest(videoUrl);
      var streamInfo = manifest.audioOnly.withHighestBitrate();

      if (streamInfo != null) {
        var stream = yt.videos.streamsClient.get(streamInfo);
        String fileExtension = streamInfo.container.name.toLowerCase();

        final Directory appDocumentsDir =
            await getApplicationDocumentsDirectory();
        var audioFile =
            File("${appDocumentsDir.path}/${videoInfo.id}.$fileExtension");
        var fileStream = audioFile.openWrite();

        await stream.pipe(fileStream);
        await fileStream.flush();
        await fileStream.close();
        return audioFile;
      } else {
        return File('dev/null');
      }
    } catch (e) {
      print("Error => generateAudioFile => $e");
      return File('dev/null');
    }
  }

  // Future<void> convertAudio(String inputPath, String outputPath) async {
  //   final cmd = [
  //     '-y', // Overwrite output file if it exists
  //     '-i', inputPath,
  //     '-vn', // Disable video
  //     '-ar', '16000', // Sample rate
  //     '-ac', '2', // Number of channels
  //     '-b:a', '128000k', // Bitrate
  //     outputPath
  //   ].join(' ');

  //   await FFmpegKit.executeAsync(
  //       // '-y -i $videoPath -ss 0.0 -t 1000000.0 -c copy $path/test.wav',
  //       // '-y -i "$videoPath" -ss 00:00:30 -t 00:01:30 -acodec copy $path/testAudio.wav',
  //       cmd, (session) async {
  //     final state =
  //         FFmpegKitConfig.sessionStateToString(await session.getState());
  //     final returnCode = await session.getReturnCode();

  //     debugPrint("FFmpeg process exited with state $state and rc $returnCode");
  //     if (returnCode!.isValueSuccess()) {
  //       debugPrint("FFmpeg processing isValueSuccess.");
  //     } else if (returnCode.isValueError()) {
  //       final logs = await session.getLogs();
  //       print("logs => $logs");
  //       debugPrint("FFmpeg processing isValueError.");
  //     } else if (returnCode.isValueCancel()) {
  //       debugPrint("FFmpeg processing isValueCancel.");
  //     }
  //     if (ReturnCode.isSuccess(returnCode)) {
  //       debugPrint('Video successfuly saved');
  //     } else {
  //       debugPrint("FFmpeg processing failed.");
  //     }
  //   });
  // }

  Future<bool> checkFileExists(String filePath) async {
    final file = File(filePath);

    if (filePath == 'dev/null') {
      print('This is /dev/null, returning false.');
      return false; // Explicitly return false for /dev/null
    }

    if (await file.exists()) {
      print('File exists.');
      return true;
    } else {
      print('File does not exist.');
      return false;
    }
  }

  Future<AudioSession> uploadAudioFileToTranscript(
      String videoUrl, String trackingId) async {
    final url = YoutubePlayer.convertUrlToId(videoUrl);
    final Directory appDocumentsDir = await getApplicationDocumentsDirectory();
    try {
      final audioFile = await generateAudioFile(url ?? "");
      String filenameWithExtension = p.basename(audioFile.path);
      print("FilePath => $filenameWithExtension");
      bool isFileExists = await checkFileExists(audioFile.path);
      if (isFileExists) {
        // await convertAudio(
        //     audioFile.path, "${appDocumentsDir.path}/$url-converted.wav");
        await Future.delayed(const Duration(seconds: 1));
        // final convertedAudio =
        //     File("${appDocumentsDir.path}/$url-converted.wav");
        final formData = FormData.fromMap({
          "file": MultipartFile.fromBytes(audioFile.readAsBytesSync(),
              filename: filenameWithExtension),
          "video_link": videoUrl,
          "tracking_id": trackingId,
        });

        final response = await dio.post(startTranscript, data: formData);
        print("response => uploadAudioFileToTranscript => ${response.data}");
        if (response.statusCode == 200 && response.data['status']) {
          // final audioFile = File("${appDocumentsDir.path}/$url.wav");
          if (await audioFile.exists()) {
            await audioFile.delete();
          }
          final audioSession = AudioSession.fromJson(jsonEncode(response.data));
          return audioSession;
        } else {
          return AudioSession(status: false, sessionId: 0);
        }
      } else {
        return AudioSession(status: false, sessionId: 0);
      }
    } catch (e) {
      print("Error => uploadAudioFileToTranscript => $e");
      return AudioSession(status: false, sessionId: 0);
    }
  }

  Future<AudioStatus> getVideoStatus(String videoUrl) async {
    print("baseUrl => $baseUrl");
    try {
      final response = await dio
          .get(checkVideoStatus, queryParameters: {"video_link": videoUrl});
      if (response.statusCode == 200 && response.data['status']) {
        final audioStatus = AudioStatus.fromJson(response.data);
        return audioStatus;
      } else {
        return AudioStatus(
            status: false,
            sessionId: 0,
            data: [],
            trackingId: response.data['tracking_id'] ?? "");
      }
    } catch (e) {
      print("Error => getVideoStatus => $e");
      return AudioStatus(status: false, sessionId: 0, data: [], trackingId: "");
    }
  }
}

List<Data> captionList = [];
ValueNotifier<bool> isCaptionLoaded = ValueNotifier(false);
