import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'dart:io' show Platform;
import 'package:flutter/services.dart';
import 'package:package_info_plus/package_info_plus.dart';

class DeviceInfoService {
  final DeviceInfoPlugin _deviceInfoPlugin = DeviceInfoPlugin();
  static const MethodChannel _channel = MethodChannel('com.asl.device_id');

  Future<Map<String, dynamic>> getAndroidId() async {
    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      final version = packageInfo.version;

      final info = await _deviceInfoPlugin.androidInfo;
      String? androidId;
      try {
        androidId = await _channel.invokeMethod('getAndroidId');
      } catch (_) {
        androidId = info.id;
      }

      return {
        'app_version': version,
        'device_info': {
          'device_id': androidId ?? 'unknown',
          'device_model': info.model,
        },
      };
    } on PlatformException catch (e) {
      return {'error': 'Failed to get Android ID: ${e.message}'};
    }
  }

  Future<Map<String, dynamic>> getDeviceInfo() async {
    try {
      if (Platform.isAndroid) {
        return await getAndroidId();
      } else if (Platform.isIOS) {
        PackageInfo packageInfo = await PackageInfo.fromPlatform();
        final version = packageInfo.version;
        final info = await _deviceInfoPlugin.iosInfo;
        return {
          'app_version': version,
          'device_info': {
            'device_id': info.identifierForVendor ?? 'unknown',
            'device_model': info.model
          },
        };
      } else {
        return {'error': 'Unsupported platform'};
      }
    } catch (e) {
      return {'error': 'Error retrieving device info: $e'};
    }
  }
}
