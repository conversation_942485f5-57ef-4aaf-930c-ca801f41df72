import 'package:flutter_test/flutter_test.dart';
import 'package:arabic_sign_language/bloc/textTranscript/text_transcript_bloc.dart';
import 'package:arabic_sign_language/data/service/text_conversion_service.dart';

// Simple mock class for TextConversionService
class MockTextConversionService implements TextConversionService {
  @override
  dynamic noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

void main() {
  group('TextTranscriptBloc Slider Tests', () {
    late TextTranscriptBloc textTranscriptBloc;
    late MockTextConversionService mockTextConversionService;

    setUp(() {
      mockTextConversionService = MockTextConversionService();
      textTranscriptBloc = TextTranscriptBloc(mockTextConversionService);
    });

    tearDown(() {
      textTranscriptBloc.close();
    });

    test('initial state should have default animation speed of 1.0', () {
      expect(textTranscriptBloc.state.animationSpeed, equals(1.0));
    });

    test('slider value conversion works correctly', () {
      // Test the conversion formula: (sliderValue * (2.0 - 0.5) / 1.5) + 0.5

      // Slider value 0.0 should map to animation speed 0.5
      double sliderValue = 0.0;
      double expectedAnimationSpeed = (sliderValue * (2.0 - 0.5) / 1.5) + 0.5;
      expect(expectedAnimationSpeed, equals(0.5));

      // Slider value 0.75 should map to animation speed 1.25
      sliderValue = 0.75;
      expectedAnimationSpeed = (sliderValue * (2.0 - 0.5) / 1.5) + 0.5;
      expect(expectedAnimationSpeed, equals(1.25));

      // Slider value 1.5 should map to animation speed 2.0
      sliderValue = 1.5;
      expectedAnimationSpeed = (sliderValue * (2.0 - 0.5) / 1.5) + 0.5;
      expect(expectedAnimationSpeed, equals(2.0));
    });

    test('animation speed clamping works correctly', () {
      // Test that values are clamped between 0.5 and 2.0
      expect(3.0.clamp(0.5, 2.0), equals(2.0));
      expect(0.1.clamp(0.5, 2.0), equals(0.5));
      expect(1.5.clamp(0.5, 2.0), equals(1.5));
    });

    test('slider value clamping works correctly', () {
      // Test that slider values are clamped between 0.0 and 1.5
      expect(2.0.clamp(0.0, 1.5), equals(1.5));
      expect((-0.5).clamp(0.0, 1.5), equals(0.0));
      expect(1.0.clamp(0.0, 1.5), equals(1.0));
    });
  });
}
